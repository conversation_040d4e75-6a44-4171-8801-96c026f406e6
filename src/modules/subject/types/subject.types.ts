export interface SubjectDetails {
  id: string;
  name: string;
  code: string;
  description?: string;
  course_id: string;
  created_at: string;
}

export interface EnrolledStudent {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  course: string;
  enrollment_date: string;
  avatar_url?: string;
}

export interface ConversationData {
  id: string;
  type: 'direct' | 'group';
  participants: string[];
  subject_id?: string;
  created_at: string;
}