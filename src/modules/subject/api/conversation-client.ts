import { ConversationData, CreateChatRequest, CreateChatResponse } from '../types';
import apiClient from '@/lib/axios';

/**
 * Client-side API functions for conversation operations
 */
export class ConversationClient {
  /**
   * Create or retrieve direct conversation between two users
   */
  static async getOrCreateDirectChat(participantId: string): Promise<{ data: CreateChatResponse | null; error: string | null }> {
    try {
      const response = await apiClient.post('/conversations/create-direct', {
        participant_id: participantId
      });
      return { data: response.data, error: null };
    } catch (error) {
      console.error('Error creating direct chat:', error);
      return { data: null, error: 'Failed to create chat' };
    }
  }

  /**
   * Get group chat for a specific subject
   */
  static async getGroupChatBySubject(subjectId: string): Promise<{ data: ConversationData | null; error: string | null }> {
    try {
      const response = await apiClient.get(`/conversations/group/${subjectId}`);
      return { data: response.data, error: null };
    } catch (error) {
      console.error('Error fetching group chat:', error);
      return { data: null, error: 'Failed to fetch group chat' };
    }
  }

  /**
   * Create a new conversation
   */
  static async createConversation(
    type: 'direct' | 'group',
    participants: string[],
    subjectId?: string
  ): Promise<{ data: ConversationData | null; error: string | null }> {
    try {
      const response = await apiClient.post('/conversations/create', {
        type,
        participants,
        subject_id: subjectId
      });
      return { data: response.data, error: null };
    } catch (error) {
      console.error('Error creating conversation:', error);
      return { data: null, error: 'Failed to create conversation' };
    }
  }
}