import { NextRequest, NextResponse } from 'next/server';
import { apiForgotPasswordSchema } from '@/modules/auth/api/auth-validations';
import { findUserByEmail, createPasswordResetToken } from '@/modules/auth/api/auth-database-server';
import { generateResetToken } from '@/lib/auth';
import { sendPasswordResetEmail } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validationResult = apiForgotPasswordSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const { email } = validationResult.data;

    // Check if user exists
    const { user, error: userError } = await findUserByEmail(email);

    if (userError || !user) {
      // Don't reveal if user exists or not for security
      return NextResponse.json(
        {
          success: true,
          message: 'If an account with this email exists, you will receive a password reset link.',
        },
        { status: 200 }
      );
    }

    // Check if user's email is verified
    if (!user.emailVerified) {
      return NextResponse.json(
        {
          success: false,
          error: 'Please verify your email address first',
        },
        { status: 400 }
      );
    }

    // Generate reset token
    const resetToken = generateResetToken();
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

    // Store reset token in database
    const { success: tokenStored, error: tokenError } = await createPasswordResetToken(
      email,
      resetToken,
      expiresAt
    );

    if (!tokenStored) {
      console.error('Failed to store reset token:', tokenError);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to generate reset token',
        },
        { status: 500 }
      );
    }

    // Send reset email
    const emailResult = await sendPasswordResetEmail(email, resetToken, user.firstName);

    if (!emailResult.success) {
      console.error('Failed to send reset email:', emailResult.error);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to send reset email',
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        success: true,
        message: 'Password reset link sent successfully. Please check your email.',
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Forgot password error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}