'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useAuthStore } from '@/stores/auth-store';
import { User } from '@/types/auth';
import { RefreshCw } from 'lucide-react';
import { useEffect, useState } from 'react';
import { DashboardLayout } from '../components/dashboard-layout';
import { DashboardStats } from '../components/dashboard-stats';
import { SubjectGrid } from '../components/subject-grid';
import { useDashboardStore } from '../store/dashboard-store';
import { useDashboard } from '../hooks/use-dashboard';

export function DashboardPage() {
   const [user, setUser] = useState<User | null>(null);
   const [isClient, setIsClient] = useState(false);
   const { data, loading, error, refresh } = useDashboard();

   useEffect(() => {
      setIsClient(true);
      const authState = useAuthStore.getState();
      setUser(authState.user);
   }, []);

   return (
      <DashboardLayout>
         <div className='space-y-6'>
            {/* Welcome Section */}
            <section className='space-y-2'>
               <div className='flex items-center justify-between'>
                  <div>
                     <h1 className='text-2xl font-bold text-gray-900'>
                        Welcome back{isClient && user?.firstName ? `, ${user.firstName}` : ''}! 👋
                     </h1>
                     <p className='text-gray-600'>
                        Here's your academic dashboard with all your enrolled subjects.
                     </p>
                  </div>

                  <Button
                     onClick={refresh}
                     variant='outline'
                     size='sm'
                     disabled={loading}
                     className='flex items-center gap-2'
                  >
                     <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                     Refresh
                  </Button>
               </div>
            </section>

            {/* Dashboard Statistics */}
            <section>
               <DashboardStats />
            </section>

            {/* Subjects Section */}
            <section className='space-y-4'>
               <div>
                  <h2 className='text-xl font-semibold text-gray-900'>Your Subjects</h2>
                  <p className='text-gray-600'>
                     {data?.subjects?.length
                        ? `Enrolled in ${data.subjects.length} subject${
                             data.subjects.length === 1 ? '' : 's'
                          }`
                        : 'No subjects enrolled yet'}
                  </p>
               </div>

               <SubjectGrid subjects={data?.subjects} loading={loading} error={error} />
            </section>
         </div>
      </DashboardLayout>
   );
}
