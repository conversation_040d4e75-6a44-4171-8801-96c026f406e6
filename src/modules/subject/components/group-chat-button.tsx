'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Users, Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { ConversationClient } from '../api';

interface GroupChatButtonProps {
  subjectId: string;
  onError?: (error: string) => void;
  className?: string;
}

export function GroupChatButton({
  subjectId,
  onError,
  className
}: GroupChatButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleGroupChat = async () => {
    setIsLoading(true);

    try {
      const { data, error } = await ConversationClient.getGroupChatBySubject(subjectId);

      if (error || !data) {
        if (onError) {
          onError(error || 'Failed to access group chat');
        }
        return;
      }

      // Navigate to group chat
      router.push(`/chat/${data.id}`);
    } catch (error) {
      console.error('Error accessing group chat:', error);
      if (onError) {
        onError('Failed to access group chat');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      onClick={handleGroupChat}
      disabled={isLoading}
      className={`flex items-center gap-2 bg-blue-600 hover:bg-blue-700 ${className}`}
    >
      {isLoading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <Users className="h-4 w-4" />
      )}
      {isLoading ? 'Accessing...' : 'Join Group Chat'}
    </Button>
  );
}