// Database types based on Supabase schema

export interface DatabaseUser {
  id: number;
  email: string;
  password_hash: string;
  first_name: string;
  last_name: string;
  phone_number: string | null;
  course_id: number;
  email_verified: boolean;
  created_at: string;
  updated_at: string;
  avatar_url?: string; // Optional field that might be added later
}

export interface UserSubjectJoin {
  user_id: number;
  enrolled_at: string;
  users: DatabaseUser;
}

// Supabase query result types
export interface SubjectStudentsQueryResult {
  user_id: number;
  enrolled_at: string;
  users: DatabaseUser | null;
}