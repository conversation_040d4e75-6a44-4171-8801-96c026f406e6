'use client';

import { useEffect, useRef } from 'react';
import {
   useSubjectStore,
   useCurrentSubject,
   useSubjectStudents,
   useSubjectLoading,
   useSubjectError,
} from '../store/subject-store';

/**
 * Main hook for subject data management following Zustand best practices
 * Uses individual selectors for reactive state and getState() for actions
 */
export function useSubject(subjectId: string) {
   // Reactive state for UI updates
   const currentSubject = useCurrentSubject();
   const students = useSubjectStudents();
   const loading = useSubjectLoading();
   const error = useSubjectError();

   // Get stable reference to store actions
   const fetchSubjectData = useSubjectStore(state => state.fetchSubjectData);
   const createDirectChat = useSubjectStore(state => state.createDirectChat);
   const clearError = useSubjectStore(state => state.clearError);

   // Track the last fetched ID to prevent duplicate calls
   const lastFetchedId = useRef<string | null>(null);

   useEffect(() => {
      if (
         subjectId &&
         (!currentSubject || currentSubject.id !== subjectId) &&
         lastFetchedId.current !== subjectId
      ) {
         lastFetchedId.current = subjectId;
         fetchSubjectData(subjectId);
      }
   }, [subjectId, currentSubject]);

   return {
      subject: currentSubject,
      students,
      loading,
      error,
      createChat: createDirectChat,
      clearError,
   };
}
