'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Users, Calendar, CheckCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface SubjectActionsProps {
  subjectId: string;
  onGroupChatClick?: () => void;
  onAttendanceClick?: () => void;
}

export function SubjectActions({
  subjectId,
  onGroupChatClick,
  onAttendanceClick
}: SubjectActionsProps) {
  const router = useRouter();

  const handleGroupChat = () => {
    if (onGroupChatClick) {
      onGroupChatClick();
    } else {
      // Default behavior - redirect to group chat
      router.push(`/chat/group/${subjectId}`);
    }
  };

  const handleAttendance = () => {
    if (onAttendanceClick) {
      onAttendanceClick();
    } else {
      // Default behavior - mark attendance for today
      console.log('Mark attendance for today');
    }
  };

  return (
    <div className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="container mx-auto">
        <div className="flex flex-col sm:flex-row gap-3">
          {/* Group Chat Button */}
          <Button
            onClick={handleGroupChat}
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
          >
            <Users className="h-4 w-4" />
            Join Group Chat
          </Button>

          {/* Quick Attendance Button */}
          <Button
            onClick={handleAttendance}
            variant="outline"
            className="flex items-center gap-2 border-green-300 text-green-700 hover:bg-green-50"
          >
            <CheckCircle className="h-4 w-4" />
            Mark Attendance
          </Button>

          {/* Attendance History Button */}
          <Button
            variant="outline"
            onClick={() => router.push(`/attendance/${subjectId}`)}
            className="flex items-center gap-2"
          >
            <Calendar className="h-4 w-4" />
            View Attendance
          </Button>
        </div>
      </div>
    </div>
  );
}