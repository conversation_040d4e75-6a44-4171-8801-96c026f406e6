import { NextRequest, NextResponse } from 'next/server';
import { apiResendOTPSchema } from '@/modules/auth/api/auth-validations';
import { findUserByEmail, createOTPRecord } from '@/modules/auth/api/auth-database-server';
import { generateOTPWithExpiry } from '@/lib/auth';
import { sendOTPEmail } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validationResult = apiResendOTPSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const { email } = validationResult.data;

    // Check if user exists
    const { user, error: userError } = await findUserByEmail(email);

    if (userError || !user) {
      return NextResponse.json(
        {
          success: false,
          error: 'No account found with this email address',
        },
        { status: 404 }
      );
    }

    // Check if user is already verified
    if (user.emailVerified) {
      return NextResponse.json(
        {
          success: false,
          error: 'Email is already verified',
        },
        { status: 400 }
      );
    }

    // Generate new OTP and expiry
    const { otp, expiresAt } = generateOTPWithExpiry();

    // Store OTP in database (this will invalidate previous OTPs)
    const { success: otpStored, error: otpError } = await createOTPRecord(
      email,
      otp,
      expiresAt
    );

    if (!otpStored) {
      console.error('Failed to store OTP:', otpError);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to generate verification code',
        },
        { status: 500 }
      );
    }

    // Send OTP email
    const emailResult = await sendOTPEmail(email, otp, user.firstName);

    if (!emailResult.success) {
      console.error('Failed to send OTP email:', emailResult.error);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to send verification email',
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        success: true,
        message: 'Verification code sent successfully. Please check your email.',
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Resend OTP error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}