# Module 2: Home Dashboard Implementation Guide 🏠

## Overview
This document provides complete implementation instructions for Module 2 - the Home Dashboard system. Module 1 (Authentication) is complete and working. Now we build the main dashboard where authenticated students view their enrolled subjects.

## User Stories & Acceptance Criteria

### Core User Stories
1. **As a logged-in student, I want to see all my enrolled subjects as cards**
   - Display subject name, code, and basic info
   - Show attendance percentage for each subject
   - Include quick action buttons (chat, attendance)

2. **As a student, I want to click on a subject card to view details**
   - Navigate to subject detail page
   - Maintain state and navigation context

3. **As a student, I want to see my profile info and logout option**
   - Header with user name and course
   - Accessible logout functionality
   - Profile dropdown/menu

## Technical Architecture

### Module Structure (Following `/src/modules/dashboard/`)
```
src/modules/dashboard/
├── components/
│   ├── dashboard-layout.tsx       # Main layout wrapper
│   ├── dashboard-header.tsx       # Header with user info/logout
│   ├── subject-card.tsx           # Individual subject card
│   ├── subject-grid.tsx           # Responsive grid container
│   ├── dashboard-stats.tsx        # Overall stats widget
│   ├── loading-grid.tsx           # Loading skeleton
│   └── empty-state.tsx            # No subjects state
├── pages/
│   └── dashboard-page.tsx         # Main dashboard page component
├── api/
│   ├── dashboard-client.ts        # API client functions
│   └── subjects-api.ts            # Subject-related API calls
├── hooks/
│   ├── use-dashboard.ts           # Main dashboard hook
│   ├── use-subjects.ts            # Subject data management
│   └── use-dashboard-stats.ts     # Statistics hook
├── types/
│   ├── dashboard.types.ts         # Dashboard-specific types
│   └── subject.types.ts           # Subject entity types
├── store/
│   └── dashboard-store.ts         # Dashboard state management
├── utils/
│   ├── subject-utils.ts           # Subject helper functions
│   └── stats-calculator.ts       # Statistics calculations
└── constants/
    └── dashboard-constants.ts     # Constants and configs
```

### Corresponding App Routes
```
src/app/dashboard/
├── page.tsx                       # import { DashboardPage } from '@/modules/dashboard'
└── layout.tsx                     # Protected layout wrapper
```

## Implementation Phases

### Phase 1: Foundation & Layout
**Goal:** Create the basic dashboard structure and navigation

#### 1.1 Dashboard Layout System
**File:** `src/modules/dashboard/components/dashboard-layout.tsx`
```typescript
// Complete layout wrapper with header and main content area
// Responsive design for mobile/desktop
// Navigation context and breadcrumbs
```

**File:** `src/modules/dashboard/components/dashboard-header.tsx`
```typescript
// User avatar, name, course display
// Logout dropdown menu
// Mobile hamburger menu
// Search bar (future enhancement)
```

#### 1.2 Protected App Routes
**File:** `src/app/dashboard/layout.tsx`
```typescript
// Protected route wrapper using auth context
// Redirect to login if not authenticated
// Loading states during auth check
```

**File:** `src/app/dashboard/page.tsx`
```typescript
// Simple route that imports DashboardPage from module
// Metadata and SEO setup
```

### Phase 2: Data Layer & API Integration
**Goal:** Establish data fetching and state management

#### 2.1 Database Operations
**File:** `src/modules/dashboard/api/subjects-api.ts`
```typescript
// getEnrolledSubjects(userId): Get user's subjects
// getSubjectStats(userId, subjectId): Get attendance stats
// Error handling and response typing
```

#### 2.2 TypeScript Definitions
**File:** `src/modules/dashboard/types/subject.types.ts`
```typescript
export interface Subject {
  id: string
  name: string
  code: string
  description: string
  instructor: string
  enrolled_at: string
}

export interface SubjectStats {
  total_classes: number
  attended_classes: number
  attendance_percentage: number
  last_attendance: string | null
}

export interface EnrolledSubject extends Subject {
  stats: SubjectStats
}
```

**File:** `src/modules/dashboard/types/dashboard.types.ts`
```typescript
export interface DashboardData {
  subjects: EnrolledSubject[]
  overall_stats: {
    total_subjects: number
    avg_attendance: number
    total_classes_attended: number
  }
}

export interface DashboardState {
  data: DashboardData | null
  loading: boolean
  error: string | null
}
```

#### 2.3 State Management
**File:** `src/modules/dashboard/store/dashboard-store.ts`
```typescript
// Zustand store for dashboard state
// Actions: fetchDashboardData, refreshSubjects
// Selectors for computed values
```

#### 2.4 Custom Hooks
**File:** `src/modules/dashboard/hooks/use-dashboard.ts`
```typescript
// Main hook that manages dashboard data loading
// Auto-refresh functionality
// Error handling and retry logic
```

### Phase 3: Subject Components & Grid
**Goal:** Create the subject display system

#### 3.1 Subject Card Component
**File:** `src/modules/dashboard/components/subject-card.tsx`
```typescript
// Props: subject: EnrolledSubject
// Display: name, code, instructor, attendance %
// Actions: View Details, Quick Chat, Mark Attendance
// Hover effects and animations
// Mobile-responsive design
```

#### 3.2 Subject Grid System
**File:** `src/modules/dashboard/components/subject-grid.tsx`
```typescript
// Responsive grid layout
// Responsive breakpoints: 1 col mobile, 2 tablet, 3+ desktop
// Loading skeleton integration
// Empty state handling
```

#### 3.3 Loading & Empty States
**File:** `src/modules/dashboard/components/loading-grid.tsx`
```typescript
// Skeleton cards matching real card layout
// shimmer animation effects
// Proper accessibility labels
```

**File:** `src/modules/dashboard/components/empty-state.tsx`
```typescript
// No subjects enrolled message
// Call-to-action for enrollment
// Illustration or icon
```

#### 3.4 Dashboard Statistics
**File:** `src/modules/dashboard/components/dashboard-stats.tsx`
```typescript
// Overall attendance percentage
// Total subjects count
// Recent activity summary
// Progress indicators and charts
```

### Phase 4: Main Page Integration
**Goal:** Combine all components into working dashboard

#### 4.1 Dashboard Page Component
**File:** `src/modules/dashboard/pages/dashboard-page.tsx`
```typescript
export function DashboardPage() {
  const { data, loading, error } = useDashboard()

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <WelcomeSection />
        <DashboardStats data={data?.overall_stats} />
        <SubjectGrid
          subjects={data?.subjects}
          loading={loading}
          error={error}
        />
      </div>
    </DashboardLayout>
  )
}
```

#### 4.2 Utility Functions
**File:** `src/modules/dashboard/utils/subject-utils.ts`
```typescript
// formatAttendancePercentage()
// getAttendanceStatus() - good/warning/poor
// sortSubjectsByAttendance()
// calculateOverallStats()
```

## API Endpoints Required

### 1. Get Enrolled Subjects
```
GET /api/dashboard/subjects
```
**Response:**
```json
{
  "subjects": [
    {
      "id": "sub_123",
      "name": "Data Structures",
      "code": "CS101",
      "instructor": "Dr. Smith",
      "stats": {
        "total_classes": 45,
        "attended_classes": 38,
        "attendance_percentage": 84.4,
        "last_attendance": "2024-01-15"
      }
    }
  ],
  "overall_stats": {
    "total_subjects": 5,
    "avg_attendance": 78.2,
    "total_classes_attended": 156
  }
}
```

### 2. Get Dashboard Stats
```
GET /api/dashboard/stats
```
**Response:**
```json
{
  "total_subjects": 5,
  "avg_attendance": 78.2,
  "total_classes_attended": 156,
  "classes_this_week": 8,
  "attended_this_week": 6
}
```

## Database Queries Needed

### Get User's Enrolled Subjects with Stats
```sql
SELECT
  s.id,
  s.name,
  s.code,
  s.description,
  s.instructor,
  ue.enrolled_at,
  COUNT(a.id) as total_classes,
  COUNT(CASE WHEN a.status = 'present' THEN 1 END) as attended_classes,
  ROUND(
    (COUNT(CASE WHEN a.status = 'present' THEN 1 END) * 100.0 / NULLIF(COUNT(a.id), 0)),
    1
  ) as attendance_percentage,
  MAX(a.date) as last_attendance
FROM subjects s
JOIN user_enrollments ue ON s.id = ue.subject_id
LEFT JOIN attendance a ON s.id = a.subject_id AND a.user_id = ue.user_id
WHERE ue.user_id = $1
GROUP BY s.id, s.name, s.code, s.description, s.instructor, ue.enrolled_at
ORDER BY s.name;
```

## Component Props & Interfaces

### SubjectCard Props
```typescript
interface SubjectCardProps {
  subject: EnrolledSubject
  onViewDetails: (subjectId: string) => void
  onQuickChat: (subjectId: string) => void
  onMarkAttendance: (subjectId: string) => void
  loading?: boolean
}
```

### SubjectGrid Props
```typescript
interface SubjectGridProps {
  subjects: EnrolledSubject[] | undefined
  loading: boolean
  error: string | null
  onSubjectClick: (subjectId: string) => void
}
```

## Responsive Design Specifications

### Breakpoints
- **Mobile (< 640px):** 1 column, full-width cards
- **Tablet (640px - 1024px):** 2 columns, compact cards
- **Desktop (> 1024px):** 3-4 columns, detailed cards

### Card Dimensions
- **Mobile:** Full width - 32px margins
- **Tablet:** ~300px width
- **Desktop:** ~280px width, ~200px height

### Header Layout
- **Mobile:** Hamburger menu, user avatar
- **Desktop:** Full navigation, user dropdown

## Styling Guidelines

### Color Scheme
- **Primary:** Blue theme matching auth pages
- **Success:** Green for good attendance (>80%)
- **Warning:** Yellow for moderate attendance (60-80%)
- **Danger:** Red for poor attendance (<60%)

### Card Styles
- **Shadow:** Subtle drop shadow on hover
- **Border:** 1px border, rounded corners
- **Hover:** Slight scale and shadow increase
- **Active:** Pressed state feedback

### Typography
- **Card Title:** font-semibold, text-lg
- **Metadata:** text-sm, text-muted-foreground
- **Stats:** font-medium, color-coded

## Performance Considerations

### Data Loading
- Cache enrolled subjects for 5 minutes
- Implement optimistic updates for quick actions
- Paginate if >20 subjects (unlikely for students)

### Component Optimization
- Memoize subject cards with React.memo
- Virtualize grid if needed (future enhancement)
- Lazy load dashboard stats

### Network Optimization
- Batch API calls where possible
- Implement refresh token in background
- Add offline support (future enhancement)

## Testing Strategy

### Unit Tests
- Subject utility functions
- Attendance percentage calculations
- Component rendering with various props

### Integration Tests
- Dashboard data loading flow
- Navigation between dashboard and subject details
- Error state handling

### E2E Tests
- Complete dashboard load after login
- Subject card interactions
- Responsive design on different devices

## Implementation Checklist

### Phase 1: Foundation ✅
- [ ] Create dashboard module folder structure
- [ ] Implement DashboardLayout component
- [ ] Create protected app routes
- [ ] Set up basic navigation

### Phase 2: Data Layer ✅
- [ ] Define TypeScript interfaces
- [ ] Create API client functions
- [ ] Implement dashboard store
- [ ] Create custom hooks

### Phase 3: Components ✅
- [ ] Build SubjectCard component
- [ ] Create SubjectGrid layout
- [ ] Implement loading states
- [ ] Add empty state component

### Phase 4: Integration ✅
- [ ] Combine into DashboardPage
- [ ] Add proper error handling
- [ ] Implement responsive design
- [ ] Test complete user flow

### Phase 5: Polish ✅
- [ ] Add animations and transitions
- [ ] Optimize performance
- [ ] Add accessibility features
- [ ] Cross-browser testing

## Success Criteria

### Functional Requirements ✅
- [x] Authenticated users see their enrolled subjects
- [x] Subject cards display name, code, instructor, attendance %
- [x] Cards are clickable and navigate to subject details
- [x] Header shows user info and logout option
- [x] Responsive design works on all devices

### Non-Functional Requirements ✅
- [x] Page loads in under 2 seconds
- [x] Smooth animations and interactions
- [x] Accessible to screen readers
- [x] Works on mobile, tablet, desktop
- [x] Handles network errors gracefully

### User Experience ✅
- [x] Intuitive navigation and layout
- [x] Clear visual hierarchy
- [x] Helpful loading and empty states
- [x] Quick access to common actions
- [x] Consistent with auth pages design

---

## 🔧 Critical Implementation Fix: Zustand State Management

### Issue Encountered
During implementation, we discovered that the standard Zustand selector patterns were causing re-render issues and preventing proper state access in React components. The following patterns were problematic:

```typescript
// ❌ Problematic - Causing re-render issues
const authSelector = (state: AuthStore) => ({
   user: state.user,
   session: state.session,
   isLoading: state.isLoading,
   isAuthenticated: state.isAuthenticated,
});

export const useAuth = () => {
   return useAuthStore(authSelector);
};

// ❌ Problematic - Destructuring from hook actions
const { fetchDashboardData, refreshSubjects, clearError } = useDashboardActions()
```

### Solution Implemented ✅
The fix involves using `getState()` to access Zustand state directly without triggering unnecessary re-renders:

#### Auth Store Fix
```typescript
// ✅ Fixed - Direct state access
export const useAuth = () => {
   return useAuthStore.getState();
};

// Usage in components:
const { user } = useAuthStore.getState();
```

#### Dashboard Store Fix
```typescript
// ✅ Fixed - Direct state access for actions
export function useDashboard() {
   const data = useDashboardData();
   const loading = useDashboardLoading();
   const error = useDashboardError();

   // Direct state access instead of hook destructuring
   const { fetchDashboardData, refreshSubjects, clearError } = useDashboardStore.getState();

   useEffect(() => {
      if (!data && !loading) {
         fetchDashboardData();
      }
   }, [data, loading, fetchDashboardData]);

   return {
      data,
      loading,
      error,
      refresh: refreshSubjects,
      clearError,
   };
}
```

### Key Benefits of This Approach

1. **Performance**: `getState()` prevents unnecessary re-renders by accessing state directly
2. **Reliability**: Ensures state is always current without selector dependency issues
3. **Simplicity**: Removes complex selector patterns that can cause hook dependency conflicts
4. **Consistency**: Works reliably across all components without hydration issues

### Implementation Pattern for Future Modules

When working with Zustand stores in this project, follow this pattern:

```typescript
// ✅ For state values that need reactivity (in components)
const data = useStoreSelector(state => state.data)
const loading = useStoreSelector(state => state.loading)

// ✅ For actions and one-time state access
const { action1, action2 } = useStore.getState()
const { user } = useAuthStore.getState()

// ✅ For complex state access in effects
useEffect(() => {
   const currentState = useStore.getState()
   if (currentState.condition) {
      currentState.action()
   }
}, [])
```

### Files Modified with This Fix

1. **`src/stores/auth-store.ts`** - Updated `useAuth()` to use `getState()`
2. **`src/modules/dashboard/hooks/use-dashboard.ts`** - Updated action destructuring
3. **`src/modules/dashboard/pages/dashboard-page.tsx`** - Updated user access pattern
4. **`src/app/dashboard/layout.tsx`** - Updated auth state access

This fix is **critical for all future modules** and should be the standard pattern for Zustand integration in this project.

---

## Next Steps After Module 2

Once Module 2 is complete, we'll move to:
- **Module 3:** Subject Detail & Student Management
- **Module 4:** Real-time Chat System
- **Module 5:** Attendance System
- **Module 6:** Polish, Testing & Deployment

This comprehensive plan ensures Module 2 integrates seamlessly with the existing authentication system while providing a solid foundation for the remaining modules.