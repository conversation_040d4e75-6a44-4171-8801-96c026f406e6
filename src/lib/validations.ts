import { z } from 'zod';

// Common validation utilities that can be used across the application
// Auth-specific validations have been moved to /src/modules/auth/api/auth-validations.ts

/**
 * Generic utility validation schemas
 */
export const requiredStringSchema = z.string().min(1, 'This field is required');
export const optionalStringSchema = z.string().optional();
export const positiveIntegerSchema = z.number().int().positive();
export const nonEmptyArraySchema = (itemSchema: z.ZodTypeAny) =>
   z.array(itemSchema).min(1, 'At least one item is required');

/**
 * Common format validations
 */
export const urlSchema = z.string().url('Please enter a valid URL');
export const phoneSchema = z
   .string()
   .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number');

/**
 * Date validation schemas
 */
export const dateSchema = z.date();
export const futureDateSchema = z
   .date()
   .refine(date => date > new Date(), 'Date must be in the future');

/**
 * File validation schemas
 */
export const fileSchema = z.object({
   name: z.string(),
   size: z.number().positive(),
   type: z.string(),
});

/**
 * Pagination validation schemas
 */
export const paginationSchema = z.object({
   page: z.number().int().positive().default(1),
   limit: z.number().int().positive().max(100).default(10),
});

// Type exports for common utilities
export type PaginationData = z.infer<typeof paginationSchema>;
export type FileData = z.infer<typeof fileSchema>;