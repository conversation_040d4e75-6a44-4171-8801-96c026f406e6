import { EnrolledSubject } from '../types/subject.types'

/**
 * Calculate attendance statistics for a group of subjects
 */
export function calculateAttendanceStats(subjects: EnrolledSubject[]) {
  if (subjects.length === 0) {
    return {
      totalSubjects: 0,
      averageAttendance: 0,
      totalClassesAttended: 0,
      totalClassesScheduled: 0,
      overallAttendanceRate: 0,
      subjectsWithGoodAttendance: 0,
      subjectsWithPoorAttendance: 0,
    }
  }

  const totalClassesAttended = subjects.reduce(
    (sum, subject) => sum + subject.stats.attended_classes,
    0
  )

  const totalClassesScheduled = subjects.reduce(
    (sum, subject) => sum + subject.stats.total_classes,
    0
  )

  const averageAttendance = subjects.reduce(
    (sum, subject) => sum + subject.stats.attendance_percentage,
    0
  ) / subjects.length

  const overallAttendanceRate = totalClassesScheduled > 0
    ? (totalClassesAttended / totalClassesScheduled) * 100
    : 0

  const subjectsWithGoodAttendance = subjects.filter(
    subject => subject.stats.attendance_percentage >= 80
  ).length

  const subjectsWithPoorAttendance = subjects.filter(
    subject => subject.stats.attendance_percentage < 60
  ).length

  return {
    totalSubjects: subjects.length,
    averageAttendance: Math.round(averageAttendance * 10) / 10,
    totalClassesAttended,
    totalClassesScheduled,
    overallAttendanceRate: Math.round(overallAttendanceRate * 10) / 10,
    subjectsWithGoodAttendance,
    subjectsWithPoorAttendance,
  }
}

/**
 * Calculate weekly attendance trend
 */
export function calculateWeeklyTrend(subjects: EnrolledSubject[]) {
  // This would require historical attendance data
  // For now, returning mock trend data
  return {
    thisWeek: 0,
    lastWeek: 0,
    trend: 'stable' as 'up' | 'down' | 'stable',
    percentageChange: 0,
  }
}

/**
 * Get attendance insights and recommendations
 */
export function getAttendanceInsights(subjects: EnrolledSubject[]) {
  const stats = calculateAttendanceStats(subjects)
  const insights: string[] = []
  const recommendations: string[] = []

  // Generate insights
  if (stats.averageAttendance >= 90) {
    insights.push('Excellent attendance record! Keep up the great work.')
  } else if (stats.averageAttendance >= 80) {
    insights.push('Good attendance overall with room for improvement.')
  } else if (stats.averageAttendance >= 70) {
    insights.push('Attendance needs attention to stay on track.')
  } else {
    insights.push('Attendance is critically low and requires immediate action.')
  }

  if (stats.subjectsWithPoorAttendance > 0) {
    insights.push(`${stats.subjectsWithPoorAttendance} subject(s) have concerning attendance rates.`)
  }

  // Generate recommendations
  if (stats.subjectsWithPoorAttendance > 0) {
    recommendations.push('Focus on improving attendance in low-performing subjects.')
    recommendations.push('Consider speaking with instructors about makeup opportunities.')
  }

  if (stats.averageAttendance < 80) {
    recommendations.push('Set reminders for upcoming classes.')
    recommendations.push('Review your schedule to identify potential conflicts.')
  }

  if (stats.subjectsWithGoodAttendance === stats.totalSubjects && stats.totalSubjects > 0) {
    recommendations.push('Maintain your excellent attendance pattern!')
  }

  return {
    insights,
    recommendations,
    priority: stats.averageAttendance < 60 ? 'high' :
             stats.averageAttendance < 80 ? 'medium' : 'low'
  }
}

/**
 * Calculate subject rankings by attendance
 */
export function calculateSubjectRankings(subjects: EnrolledSubject[]) {
  return subjects
    .map((subject, index) => ({
      ...subject,
      rank: index + 1,
      percentile: ((subjects.length - index) / subjects.length) * 100,
    }))
    .sort((a, b) => b.stats.attendance_percentage - a.stats.attendance_percentage)
    .map((subject, index) => ({
      ...subject,
      rank: index + 1,
    }))
}