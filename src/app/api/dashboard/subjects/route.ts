import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/api-auth';
import { getEnrolledSubjects } from '@/modules/dashboard/api/subjects-api';

export const GET = withAuth(async (request: NextRequest, { user }) => {
   try {
      const { data, error } = await getEnrolledSubjects(user.userId);

      if (error) {
         return NextResponse.json({ error }, { status: 500 });
      }

      return NextResponse.json(data);
   } catch (error) {
      console.error('Dashboard API error:', error);
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
   }
});