'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { loginSchema, LoginFormData } from '@/modules/auth/api/auth-validations';
import { useAuthActions } from '@/stores/auth-store';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { PasswordInput } from '@/components/ui/password-input';
import {
   Form,
   FormControl,
   FormField,
   FormItem,
   FormLabel,
   FormMessage,
} from '@/components/ui/form';
import { toast } from '@/lib/toast';

export function LoginForm() {
   const [isSubmitting, setIsSubmitting] = useState(false);
   const { login } = useAuthActions();
   const router = useRouter();

   const form = useForm<LoginFormData>({
      resolver: zodResolver(loginSchema),
      defaultValues: {
         email: '',
         password: '',
      },
   });

   const onSubmit = async (data: LoginFormData) => {
      setIsSubmitting(true);

      try {
         const result = await login(data.email, data.password);
         console.log({ result: result });

         if (result.success) {
            toast.success('Login successful!');
            // Redirect to dashboard after successful login
            router.push('/dashboard/home');
         } else {
            if (result.error?.includes('verify your email')) {
               toast.error(result.error);
               // Optionally redirect to verification page
               const shouldVerify = confirm('Would you like to verify your email now?');
               if (shouldVerify) {
                  router.push(`/auth/verify-otp?email=${encodeURIComponent(data.email)}`);
               }
            } else {
               toast.error(result.error || 'Login failed');
            }
         }
      } catch (error) {
         console.error('Login error:', error);
         toast.error('Something went wrong. Please try again.');
      } finally {
         setIsSubmitting(false);
      }
   };

   return (
      <Card className='w-full max-w-md mx-auto'>
         <CardHeader>
            <CardTitle className='text-2xl'>Login to your account</CardTitle>
            <CardDescription>Enter your email below to login to your account</CardDescription>
         </CardHeader>
         <CardContent>
            <Form {...form}>
               <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
                  <FormField
                     control={form.control}
                     name='email'
                     render={({ field }) => (
                        <FormItem>
                           <FormLabel>Email</FormLabel>
                           <FormControl>
                              <Input
                                 type='email'
                                 placeholder='<EMAIL>'
                                 autoComplete='email'
                                 {...field}
                              />
                           </FormControl>
                           <FormMessage />
                        </FormItem>
                     )}
                  />

                  <FormField
                     control={form.control}
                     name='password'
                     render={({ field }) => (
                        <FormItem>
                           <div className='flex items-center justify-between'>
                              <FormLabel>Password</FormLabel>
                              <Link
                                 href='/auth/forgot-password'
                                 className='text-sm underline underline-offset-4 hover:text-primary'
                              >
                                 Forgot your password?
                              </Link>
                           </div>
                           <FormControl>
                              <PasswordInput
                                 placeholder='Enter your password'
                                 autoComplete='current-password'
                                 {...field}
                              />
                           </FormControl>
                           <FormMessage />
                        </FormItem>
                     )}
                  />

                  <Button type='submit' className='w-full' disabled={isSubmitting}>
                     {isSubmitting ? 'Logging in...' : 'Login'}
                  </Button>
               </form>
            </Form>

            <div className='mt-4 text-center text-sm'>
               Don't have an account?{' '}
               <Link
                  href='/auth/sign-up'
                  className='underline underline-offset-4 hover:text-primary'
               >
                  Sign up
               </Link>
            </div>
         </CardContent>
      </Card>
   );
}
