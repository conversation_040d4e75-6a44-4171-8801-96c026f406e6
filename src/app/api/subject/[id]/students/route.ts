import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/api-auth';
import { getSubjectStudents } from '@/modules/subject/api/subject-server-api';
import { subjectParamsSchema } from '@/modules/subject/schemas';

export const GET = withAuth(async (request: NextRequest, { user }) => {
  try {
    // Extract subject ID from URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const subjectId = pathParts[pathParts.indexOf('subject') + 1];

    // Validate subject ID
    const validation = subjectParamsSchema.safeParse({ id: subjectId });
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid subject ID format' },
        { status: 400 }
      );
    }

    // Get subject students
    const { data, error } = await getSubjectStudents(subjectId, user.userId);

    if (error) {
      return NextResponse.json({ error }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Subject students API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});