'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { signupSchema, SignupFormData } from '@/modules/auth/api/auth-validations';
import { useAuthActions } from '@/stores/auth-store';
import { getCourses } from '@/modules/auth/api/courses-client';
import { Course } from '@/types/auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { PasswordInput } from '@/components/ui/password-input';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import {
   Form,
   FormControl,
   FormField,
   FormItem,
   FormLabel,
   FormMessage,
} from '@/components/ui/form';
import { toast } from 'sonner';

export function SignupForm() {
   const [isSubmitting, setIsSubmitting] = useState(false);
   const [courses, setCourses] = useState<Course[]>([]);
   const [isLoadingCourses, setIsLoadingCourses] = useState(true);
   const { signup } = useAuthActions();
   const router = useRouter();

   const form = useForm<SignupFormData>({
      resolver: zodResolver(signupSchema),
      defaultValues: {
         firstName: '',
         lastName: '',
         email: '',
         password: '',
         confirmPassword: '',
         courseId: undefined,
      },
   });

   // Fetch courses on component mount
   useEffect(() => {
      const fetchCourses = async () => {
         try {
            const { courses: courseData, error } = await getCourses();
            if (error) {
               toast.error('Failed to load courses');
               console.error('Error fetching courses:', error);
            } else if (courseData) {
               setCourses(courseData);
            }
         } catch (error) {
            toast.error('Failed to load courses');
            console.error('Error fetching courses:', error);
         } finally {
            setIsLoadingCourses(false);
         }
      };

      fetchCourses();
   }, []);

   const onSubmit = async (data: SignupFormData) => {
      setIsSubmitting(true);

      try {
         const result = await signup({
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            password: data.password,
            courseId: data.courseId,
         });

         if (result.success) {
            toast.success(result.message || 'Account created successfully!');
            // Redirect to OTP verification page with email
            router.push(`/auth/verify-otp?email=${encodeURIComponent(data.email)}`);
         } else {
            toast.error(result.error || 'Failed to create account');
         }
      } catch (error) {
         console.error('Signup error:', error);
         toast.error('Something went wrong. Please try again.');
      } finally {
         setIsSubmitting(false);
      }
   };

   return (
      <Card className='w-full max-w-md mx-auto'>
         <CardHeader>
            <CardTitle className='text-2xl'>Create your account</CardTitle>
            <CardDescription>
               Enter your details below to create your Course Management account
            </CardDescription>
         </CardHeader>
         <CardContent>
            <Form {...form}>
               <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
                  <div className='grid grid-cols-2 gap-4'>
                     <FormField
                        control={form.control}
                        name='firstName'
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel>First Name</FormLabel>
                              <FormControl>
                                 <Input placeholder='John' {...field} />
                              </FormControl>
                              <FormMessage />
                           </FormItem>
                        )}
                     />
                     <FormField
                        control={form.control}
                        name='lastName'
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel>Last Name</FormLabel>
                              <FormControl>
                                 <Input placeholder='Doe' {...field} />
                              </FormControl>
                              <FormMessage />
                           </FormItem>
                        )}
                     />
                  </div>

                  <FormField
                     control={form.control}
                     name='email'
                     render={({ field }) => (
                        <FormItem>
                           <FormLabel>Email</FormLabel>
                           <FormControl>
                              <Input type='email' placeholder='<EMAIL>' {...field} />
                           </FormControl>
                           <FormMessage />
                        </FormItem>
                     )}
                  />

                  <FormField
                     control={form.control}
                     name='courseId'
                     render={({ field }) => (
                        <FormItem>
                           <FormLabel>Course</FormLabel>
                           <Select
                              onValueChange={value => field.onChange(parseInt(value))}
                              defaultValue={field.value?.toString()}
                              disabled={isLoadingCourses}
                           >
                              <FormControl>
                                 <SelectTrigger className='w-full'>
                                    <SelectValue
                                       placeholder={
                                          isLoadingCourses
                                             ? 'Loading courses...'
                                             : 'Select your course'
                                       }
                                    />
                                 </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                 {courses.map(course => (
                                    <SelectItem key={course.id} value={course.id.toString()}>
                                       {course.name} ({course.code})
                                    </SelectItem>
                                 ))}
                              </SelectContent>
                           </Select>
                           <FormMessage />
                        </FormItem>
                     )}
                  />

                  <FormField
                     control={form.control}
                     name='password'
                     render={({ field }) => (
                        <FormItem>
                           <FormLabel>Password</FormLabel>
                           <FormControl>
                              <PasswordInput
                                 placeholder='Create a strong password'
                                 {...field}
                              />
                           </FormControl>
                           <FormMessage />
                        </FormItem>
                     )}
                  />

                  <FormField
                     control={form.control}
                     name='confirmPassword'
                     render={({ field }) => (
                        <FormItem>
                           <FormLabel>Confirm Password</FormLabel>
                           <FormControl>
                              <PasswordInput
                                 placeholder='Confirm your password'
                                 {...field}
                              />
                           </FormControl>
                           <FormMessage />
                        </FormItem>
                     )}
                  />

                  <Button type='submit' className='w-full' disabled={isSubmitting}>
                     {isSubmitting ? 'Creating account...' : 'Create account'}
                  </Button>
               </form>
            </Form>

            <div className='mt-4 text-center text-sm'>
               Already have an account?{' '}
               <Link href='/auth/login' className='underline underline-offset-4 hover:text-primary'>
                  Sign in
               </Link>
            </div>
         </CardContent>
      </Card>
   );
}