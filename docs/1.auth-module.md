# Module 1: Custom Authentication System Implementation Plan

## Overview
Replace existing Supabase auth with custom JWT-based authentication system using OTP email verification, Zustand state management, and modular architecture.

## Phase 1: Dependencies & Infrastructure Setup (1-2 hours)

### 1.1 Install Required Dependencies
```bash
pnpm install zustand bcryptjs jsonwebtoken resend zod react-hook-form @hookform/resolvers
pnpm install -D @types/bcryptjs @types/jsonwebtoken
```

### 1.2 Add Missing shadcn Components
```bash
pnpm dlx shadcn@latest add form toast skeleton avatar select
```

**Note:** `input-otp` component is already installed - use this for OTP verification forms.

## Phase 2: Core Infrastructure (2-3 hours)

### 2.1 Utility Libraries
- `lib/auth.ts` - Password hashing, JWT utilities, OTP generation
- `lib/email.ts` - Resend integration for OTP emails
- `lib/validations.ts` - Zod schemas for form validation
- `types/auth.ts` - TypeScript interfaces

### 2.2 Email Templates
- `templates/otp-email.html` - HTML email template for OTP delivery

### 2.3 Database Helper Functions
- `lib/database.ts` - Custom user operations using Supabase as database only

## Phase 3: Zustand Store Setup (1 hour)

### 3.1 Auth Store
- `stores/auth-store.ts` - Global auth state with Zustand
- Replace React Context approach with Zustand hooks
- Persist auth state to localStorage

## Phase 4: Custom API Routes (3-4 hours)

### 4.1 Authentication APIs
- `app/api/auth/signup/route.ts` - User registration + OTP sending
- `app/api/auth/verify-otp/route.ts` - OTP verification + auto-login
- `app/api/auth/login/route.ts` - User login + JWT session
- `app/api/auth/logout/route.ts` - Session destruction
- `app/api/auth/resend-otp/route.ts` - Resend OTP functionality
- `app/api/auth/forgot-password/route.ts` - Password reset initiation
- `app/api/auth/reset-password/route.ts` - Password reset completion

## Phase 5: Auth Components Refactor (4-5 hours)

### 5.1 Clean Up Existing Components
- Remove existing Supabase auth components from `/src/components/`
- Keep only reusable UI components

### 5.2 New Module Components (using react-hook-form + shadcn/ui)
- `modules/auth/components/signup-form.tsx` - Registration with course selection using react-hook-form + shadcn Form components
- `modules/auth/components/otp-form.tsx` - OTP verification form using react-hook-form + shadcn input-otp component
- `modules/auth/components/login-form.tsx` - Custom login form using react-hook-form + shadcn Form components
- `modules/auth/components/forgot-password-form.tsx` - Password reset request using react-hook-form + shadcn Form components
- `modules/auth/components/reset-password-form.tsx` - New password form using react-hook-form + shadcn Form components

**Important:** Use shadcn/ui components wherever possible for consistency and accessibility.

### 5.3 Auth Pages
- `modules/auth/pages/signup-page.tsx` - Complete signup page
- `modules/auth/pages/verify-otp-page.tsx` - OTP verification page
- `modules/auth/pages/login-page.tsx` - Updated login page
- `modules/auth/pages/forgot-password-page.tsx` - Password reset page
- `modules/auth/pages/reset-password-page.tsx` - Password reset completion

## Phase 6: Route Protection & Integration (2-3 hours)

### 6.1 Custom Middleware
- Replace Supabase middleware with custom JWT verification
- Protect routes based on custom auth state

### 6.2 Protected Route Component
- `modules/auth/components/protected-route.tsx` - Route protection wrapper
- `hooks/use-auth.ts` - Custom auth hook using Zustand

### 6.3 Navigation Updates
- Update existing routes to use new auth pages
- Implement proper redirects after authentication

## Phase 7: Testing & Polish (2-3 hours)

### 7.1 Complete Flow Testing
- Test signup � OTP � auto-login flow end-to-end
- Verify email delivery with Resend
- Test session persistence and logout

### 7.2 Error Handling & UX
- Add comprehensive error handling
- Implement loading states throughout
- Add proper form validation feedback

### 7.3 Environment Configuration
- Update environment variables for custom auth
- Test email delivery in development

## Phase 8: Documentation & Cleanup (1 hour)

### 8.1 Update Module Documentation
- Document the new auth flow in `docs/module-1-auth.md`
- Include setup instructions and usage examples

### 8.2 Code Cleanup
- Remove unused Supabase auth code
- Ensure consistent code style throughout
- Add proper TypeScript types

## Expected Outcomes
-  Complete custom authentication system replacing Supabase auth
-  OTP email verification with auto-login flow
-  Zustand-based state management
-  Modular, maintainable code structure
-  Production-ready auth system matching PRD requirements
-  Foundation for remaining modules

## Time Estimate: 15-20 hours total

This plan maintains high code quality while building a robust foundation for the entire application. Each phase builds incrementally, allowing for testing and validation at each step.

## Current State Analysis

**Existing Setup:**
- Next.js 15.5.4 with TypeScript and Tailwind CSS 
- Supabase configured (client, server, middleware) 
- shadcn/ui components installed 
- Module structure started (`/src/modules/auth/`, `/src/modules/home/<USER>
- Basic Supabase auth components exist but need replacement L

**Key Changes Needed:**
- Replace Supabase built-in auth with custom JWT authentication
- Implement OTP email verification flow with Resend
- Use Zustand instead of React Context
- Follow modules folder structure consistently
- Create custom API routes for all auth operations

## Architecture Decisions

### Why Custom Auth Over Supabase Auth?
- Full control over user flow and experience
- Custom OTP email templates and branding
- Easier integration with course selection during signup
- Better alignment with PRD requirements
- No vendor lock-in for authentication logic

### Why Zustand Over React Context?
- Smaller bundle size and better performance
- No provider wrapping needed
- Easier to use and less boilerplate
- Built-in persistence support
- Better TypeScript integration

### Database Strategy
- Keep Supabase as database layer only
- Use custom API routes for all auth operations
- Maintain RLS policies for data security
- Custom user management with additional fields (course selection)

## Implementation Details

### JWT Strategy
- Access tokens: Short-lived (15 minutes)
- Refresh tokens: Long-lived (7 days)
- Secure HTTP-only cookies for token storage
- Automatic token refresh handling

### OTP Email Flow
1. User signs up with email + course selection
2. Generate 6-digit OTP with 10-minute expiry
3. Send branded email via Resend
4. User enters OTP for verification
5. Auto-login after successful verification
6. Welcome email sent with course information

### Security Considerations
- Password hashing with bcrypt (12 rounds)
- Rate limiting on auth endpoints
- CSRF protection with tokens
- Input validation with Zod schemas
- Secure session management
- Password strength requirements

### Error Handling Strategy
- User-friendly error messages
- Proper HTTP status codes
- Logging for debugging
- Graceful fallbacks
- Network error handling
- Form validation feedback

This comprehensive plan ensures a production-ready authentication system that perfectly aligns with the PRD requirements while maintaining code quality and security best practices.

## Email Template Implementation

### OTP Verification Email Template
Create `/templates/otp-email.html` with the following content:

```html
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html dir="ltr" lang="en">
  <head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
    <meta name="x-apple-disable-message-reformatting" />
  </head>
  <body style="background-color:#f8fafc;margin:0;padding:0">
    <table
      border="0"
      width="100%"
      cellpadding="0"
      cellspacing="0"
      role="presentation"
      align="center"
      style="margin:0;padding:0;width:100%">
      <tbody>
        <tr>
          <td
            style='background-color:#f8fafc;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;text-align:center;padding:0;margin:0'>
            <div
              style="display:none;overflow:hidden;line-height:1px;opacity:0;max-height:0;max-width:0"
              data-skip-in-text="true">
              Your verification code for Course Management.
            </div>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="max-width:37.5em;margin:40px auto;padding:40px 32px;background-color:#ffffff;border-radius:12px;box-shadow:0 4px 6px -1px rgba(0, 0, 0, 0.1)">
              <tbody>
                <tr style="width:100%">
                  <td>
                    <div style="text-align:center;margin-bottom:32px">
                      <h1 style="font-size:24px;font-weight:600;color:#1e40af;margin:0;letter-spacing:-0.025em">
                        Course Management
                      </h1>
                    </div>

                    <h2 style="font-size:24px;font-weight:600;margin-top:0;margin-bottom:24px;color:#1f2937">
                      Verify your email address
                    </h2>

                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="margin:32px 0">
                      <tbody>
                        <tr>
                          <td>
                            <p style="font-size:16px;line-height:24px;margin-top:0;margin-bottom:24px;color:#4b5563">
                              Thanks for starting the Course Management registration process. We want to make sure it's really you. Please enter the following verification code when prompted.
                            </p>

                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="margin:32px 0;text-align:center">
                              <tbody>
                                <tr>
                                  <td>
                                    <p style="font-size:14px;line-height:20px;margin:0;margin-bottom:8px;color:#6b7280;font-weight:600">
                                      Verification Code
                                    </p>
                                    <div style="background-color:#f8fafc;border:2px solid #e5e7eb;border-radius:8px;padding:20px;margin:16px 0;display:inline-block">
                                      <p style="font-size:32px;line-height:1;margin:0;font-weight:700;color:#1f2937;letter-spacing:0.1em;font-family:Monaco,Menlo,Consolas,'Courier New',monospace">
                                        {{ .VerificationCode }}
                                      </p>
                                    </div>
                                    <p style="font-size:14px;line-height:20px;margin:8px 0 0 0;color:#9ca3af">
                                      (This code is valid for 10 minutes)
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>

                            <p style="font-size:14px;line-height:20px;margin-top:32px;margin-bottom:16px;color:#6b7280">
                              If you didn't request this verification code, please ignore this email.
                            </p>
                          </td>
                        </tr>
                      </tbody>
                    </table>

                    <p style="font-size:16px;line-height:24px;margin-top:32px;margin-bottom:0;color:#4b5563">
                      Best regards,<br />
                      The Course Management Team
                    </p>

                    <hr style="width:100%;border:none;border-top:1px solid #e5e7eb;margin-top:40px;margin-bottom:24px" />

                    <p style="font-size:12px;line-height:16px;color:#9ca3af;margin:0;text-align:center">
                      Course Management Platform
                    </p>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </body>
</html>
```

### Email Implementation Notes
- Replace `{{ .VerificationCode }}` with the actual OTP when sending
- Template uses clean, professional styling matching the application design
- Responsive design that works across email clients
- 10-minute expiry clearly communicated to users
- Includes security disclaimer for unintended requests

## React Hook Form Integration

### Form Implementation Standards
- Use `react-hook-form` with `@hookform/resolvers/zod` for all forms
- **Always use shadcn/ui components** for consistent styling and accessibility:
  - `Form`, `FormControl`, `FormField`, `FormItem`, `FormLabel`, `FormMessage` for form structure
  - `Input` for text inputs
  - `Button` for actions
  - `InputOTP` for OTP verification (already installed)
  - `Select` for dropdown selections
  - `Card` for form containers
- Implement proper TypeScript types for form data
- Include proper validation with Zod schemas
- Handle loading states and error messages consistently

### Example Form Structure:
```typescript
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const formSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

type FormData = z.infer<typeof formSchema>;

export function LoginForm() {
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Login</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* More fields... */}
            <Button type="submit" className="w-full">Login</Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
```

### OTP Form Example with InputOTP:
```typescript
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';

// In your OTP form component:
<FormField
  control={form.control}
  name="otp"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Verification Code</FormLabel>
      <FormControl>
        <InputOTP maxLength={6} {...field}>
          <InputOTPGroup>
            <InputOTPSlot index={0} />
            <InputOTPSlot index={1} />
            <InputOTPSlot index={2} />
            <InputOTPSlot index={3} />
            <InputOTPSlot index={4} />
            <InputOTPSlot index={5} />
          </InputOTPGroup>
        </InputOTP>
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>
```