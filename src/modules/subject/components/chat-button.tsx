'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { MessageCircle, Loader2 } from 'lucide-react';

interface ChatButtonProps {
  onClick: () => Promise<void> | void;
  disabled?: boolean;
  loading?: boolean;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  children?: React.ReactNode;
  className?: string;
}

export function ChatButton({
  onClick,
  disabled = false,
  loading = false,
  variant = 'outline',
  size = 'sm',
  children,
  className
}: ChatButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    if (disabled || loading || isLoading) return;

    setIsLoading(true);
    try {
      await onClick();
    } catch (error) {
      console.error('Chat button error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const isButtonLoading = loading || isLoading;

  return (
    <Button
      onClick={handleClick}
      disabled={disabled || isButtonLoading}
      variant={variant}
      size={size}
      className={className}
    >
      {isButtonLoading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <MessageCircle className="h-4 w-4" />
      )}
      {children && <span className="ml-2">{children}</span>}
    </Button>
  );
}