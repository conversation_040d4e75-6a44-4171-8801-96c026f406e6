import { createClient } from '@/lib/supabase/server';
import { User, DatabaseUser, OTPRecord, PasswordResetToken, SignupData } from '@/types/auth';
import { hashPassword } from '@/lib/auth';
import { isAfter, parseISO } from 'date-fns';

/**
 * Convert database user to public user interface
 */
function dbUserToUser(dbUser: DatabaseUser): User {
  return {
    id: dbUser.id,
    email: dbUser.email,
    firstName: dbUser.first_name,
    lastName: dbUser.last_name,
    courseId: dbUser.course_id,
    emailVerified: dbUser.email_verified,
    createdAt: dbUser.created_at,
    updatedAt: dbUser.updated_at,
  };
}

/**
 * Create a new user in the database
 */
export async function createUser(userData: SignupData): Promise<{ user: User | null; error: string | null }> {
  try {
    const supabase = await createClient();
    const hashedPassword = await hashPassword(userData.password);

    const { data, error } = await supabase
      .from('users')
      .insert({
        email: userData.email.toLowerCase().trim(),
        first_name: userData.firstName.trim(),
        last_name: userData.lastName.trim(),
        course_id: userData.courseId,
        password_hash: hashedPassword,
        email_verified: false,
      })
      .select()
      .single();

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        return { user: null, error: 'An account with this email already exists' };
      }
      console.error('Error creating user:', error);
      return { user: null, error: 'Failed to create account' };
    }

    return { user: dbUserToUser(data as DatabaseUser), error: null };
  } catch (error) {
    console.error('Database error creating user:', error);
    return { user: null, error: 'Database error occurred' };
  }
}

/**
 * Find user by email
 */
export async function findUserByEmail(email: string): Promise<{ user: User | null; error: string | null }> {
  try {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email.toLowerCase().trim())
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return { user: null, error: null };
      }
      console.error('Error finding user by email:', error);
      return { user: null, error: 'Database error occurred' };
    }

    return { user: dbUserToUser(data as DatabaseUser), error: null };
  } catch (error) {
    console.error('Database error finding user by email:', error);
    return { user: null, error: 'Database error occurred' };
  }
}

/**
 * Find user by ID
 */
export async function findUserById(id: string): Promise<{ user: User | null; error: string | null }> {
  try {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return { user: null, error: null };
      }
      console.error('Error finding user by ID:', error);
      return { user: null, error: 'Database error occurred' };
    }

    return { user: dbUserToUser(data as DatabaseUser), error: null };
  } catch (error) {
    console.error('Database error finding user by ID:', error);
    return { user: null, error: 'Database error occurred' };
  }
}

/**
 * Get user password hash by email
 */
export async function getUserPasswordHash(email: string): Promise<{ passwordHash: string | null; error: string | null }> {
  try {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from('users')
      .select('password_hash')
      .eq('email', email.toLowerCase().trim())
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return { passwordHash: null, error: null };
      }
      console.error('Error getting user password hash:', error);
      return { passwordHash: null, error: 'Database error occurred' };
    }

    return { passwordHash: data.password_hash, error: null };
  } catch (error) {
    console.error('Database error getting password hash:', error);
    return { passwordHash: null, error: 'Database error occurred' };
  }
}

/**
 * Update user email verification status
 */
export async function updateUserEmailVerification(email: string, verified: boolean): Promise<{ success: boolean; error: string | null }> {
  try {
    const supabase = await createClient();

    const { error } = await supabase
      .from('users')
      .update({
        email_verified: verified,
        updated_at: new Date().toISOString()
      })
      .eq('email', email.toLowerCase().trim());

    if (error) {
      console.error('Error updating user email verification:', error);
      return { success: false, error: 'Failed to update verification status' };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Database error updating email verification:', error);
    return { success: false, error: 'Database error occurred' };
  }
}

/**
 * Update user password
 */
export async function updateUserPassword(email: string, newPassword: string): Promise<{ success: boolean; error: string | null }> {
  try {
    const supabase = await createClient();
    const hashedPassword = await hashPassword(newPassword);

    const { error } = await supabase
      .from('users')
      .update({
        password_hash: hashedPassword,
        updated_at: new Date().toISOString()
      })
      .eq('email', email.toLowerCase().trim());

    if (error) {
      console.error('Error updating user password:', error);
      return { success: false, error: 'Failed to update password' };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Database error updating password:', error);
    return { success: false, error: 'Database error occurred' };
  }
}

/**
 * Create OTP record
 */
export async function createOTPRecord(email: string, otp: string, expiresAt: Date): Promise<{ success: boolean; error: string | null }> {
  try {
    const supabase = await createClient();

    // First, get user ID from email
    const { user: existingUser, error: userError } = await findUserByEmail(email);
    if (userError || !existingUser) {
      return { success: false, error: 'User not found' };
    }

    // First, invalidate any existing OTPs for this user
    await supabase
      .from('otp_verifications')
      .update({ is_used: true })
      .eq('user_id', existingUser.id)
      .eq('is_used', false);

    const expiresAtISO = expiresAt.toISOString();

    const { error } = await supabase
      .from('otp_verifications')
      .insert({
        user_id: existingUser.id,
        otp_code: otp,
        purpose: 'email_verification',
        expires_at: expiresAtISO,
        is_used: false,
      });

    if (error) {
      console.error('Error creating OTP record:', error);
      return { success: false, error: 'Failed to create OTP record' };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Database error creating OTP record:', error);
    return { success: false, error: 'Database error occurred' };
  }
}

/**
 * Verify OTP and mark as used
 */
export async function verifyAndUseOTP(email: string, otp: string): Promise<{ valid: boolean; error: string | null }> {
  try {
    const supabase = await createClient();

    // First, get user ID from email
    const { user: existingUser, error: userError } = await findUserByEmail(email);
    if (userError || !existingUser) {
      return { valid: false, error: 'User not found' };
    }

    const { data, error } = await supabase
      .from('otp_verifications')
      .select('*')
      .eq('user_id', existingUser.id)
      .eq('otp_code', otp)
      .eq('is_used', false)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return { valid: false, error: 'Invalid OTP' };
      }
      console.error('Error verifying OTP:', error);
      return { valid: false, error: 'Database error occurred' };
    }

    // Check if OTP is expired using date-fns for timezone-safe comparison
    const currentTime = new Date();
    // Ensure the expires_at has timezone info - if missing 'Z', add it for UTC
    const expiresAtString = data.expires_at.endsWith('Z') ? data.expires_at : data.expires_at + 'Z';
    const expiryTime = parseISO(expiresAtString);

    if (isAfter(currentTime, expiryTime)) {
      return { valid: false, error: 'OTP has expired' };
    }

    // Mark OTP as used
    const { error: updateError } = await supabase
      .from('otp_verifications')
      .update({ is_used: true })
      .eq('id', data.id);

    if (updateError) {
      console.error('Error marking OTP as used:', updateError);
      return { valid: false, error: 'Failed to verify OTP' };
    }

    return { valid: true, error: null };
  } catch (error) {
    console.error('Database error verifying OTP:', error);
    return { valid: false, error: 'Database error occurred' };
  }
}

/**
 * Create password reset token
 */
export async function createPasswordResetToken(email: string, token: string, expiresAt: Date): Promise<{ success: boolean; error: string | null }> {
  try {
    const supabase = await createClient();

    // First, get user ID from email
    const { user: existingUser, error: userError } = await findUserByEmail(email);
    if (userError || !existingUser) {
      return { success: false, error: 'User not found' };
    }

    // First, invalidate any existing reset tokens for this user
    await supabase
      .from('password_resets')
      .update({ is_used: true })
      .eq('user_id', existingUser.id)
      .eq('is_used', false);

    const { error } = await supabase
      .from('password_resets')
      .insert({
        user_id: existingUser.id,
        reset_token: token,
        expires_at: expiresAt.toISOString(),
        is_used: false,
      });

    if (error) {
      console.error('Error creating password reset token:', error);
      return { success: false, error: 'Failed to create reset token' };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Database error creating password reset token:', error);
    return { success: false, error: 'Database error occurred' };
  }
}

/**
 * Verify password reset token and mark as used
 */
export async function verifyAndUseResetToken(token: string): Promise<{ valid: boolean; email: string | null; error: string | null }> {
  try {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from('password_resets')
      .select('*, users(email)')
      .eq('reset_token', token)
      .eq('is_used', false)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return { valid: false, email: null, error: 'Invalid reset token' };
      }
      console.error('Error verifying reset token:', error);
      return { valid: false, email: null, error: 'Database error occurred' };
    }

    // Check if token is expired using date-fns for timezone-safe comparison
    const currentTime = new Date();
    const expiresAtString = data.expires_at.endsWith('Z') ? data.expires_at : data.expires_at + 'Z';
    const expiryTime = parseISO(expiresAtString);

    if (isAfter(currentTime, expiryTime)) {
      return { valid: false, email: null, error: 'Reset token has expired' };
    }

    // Mark token as used
    const { error: updateError } = await supabase
      .from('password_resets')
      .update({ is_used: true })
      .eq('id', data.id);

    if (updateError) {
      console.error('Error marking reset token as used:', updateError);
      return { valid: false, email: null, error: 'Failed to verify reset token' };
    }

    return { valid: true, email: data.users?.email || null, error: null };
  } catch (error) {
    console.error('Database error verifying reset token:', error);
    return { valid: false, email: null, error: 'Database error occurred' };
  }
}

/**
 * Clean up expired OTPs and reset tokens (utility function for cleanup jobs)
 */
export async function cleanupExpiredTokens(): Promise<{ success: boolean; error: string | null }> {
  try {
    const supabase = await createClient();
    const now = new Date().toISOString();

    // Clean up expired OTPs
    await supabase
      .from('otp_verifications')
      .update({ is_used: true })
      .lt('expires_at', now)
      .eq('is_used', false);

    // Clean up expired reset tokens
    await supabase
      .from('password_resets')
      .update({ is_used: true })
      .lt('expires_at', now)
      .eq('is_used', false);

    return { success: true, error: null };
  } catch (error) {
    console.error('Database error cleaning up expired tokens:', error);
    return { success: false, error: 'Database error occurred' };
  }
}