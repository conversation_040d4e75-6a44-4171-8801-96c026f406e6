'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { SubjectHeader } from '../components/subject-header';
import { SubjectActions } from '../components/subject-actions';
import { StudentGrid } from '../components/student-grid';
import { GroupChatButton } from '../components/group-chat-button';
import { AttendanceQuickAction } from '../components/attendance-quick-action';
import { useSubject } from '../hooks/use-subject';

interface SubjectDetailPageProps {
  subjectId: string;
}

export function SubjectDetailPage({ subjectId }: SubjectDetailPageProps) {
  const { subject, students, loading, error, createChat, clearError } = useSubject(subjectId);
  const [creatingChat, setCreatingChat] = useState(false);
  const [creatingChatForStudent, setCreatingChatForStudent] = useState<string | null>(null);
  const router = useRouter();

  const handleStartChat = async (studentId: string) => {
    setCreatingChat(true);
    setCreatingChatForStudent(studentId);

    try {
      const conversationId = await createChat(studentId);
      router.push(`/chat/${conversationId}`);
    } catch (error) {
      console.error('Failed to create chat:', error);
      // Error will be shown via the store's error state
    } finally {
      setCreatingChat(false);
      setCreatingChatForStudent(null);
    }
  };

  const handleGroupChat = () => {
    // This will be handled by the GroupChatButton component
  };

  const handleAttendance = () => {
    // TODO: Implement attendance marking
    console.log('Mark attendance for today');
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="mx-auto max-w-md">
              <div className="mx-auto h-24 w-24 text-red-400">
                <svg
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-full h-full"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"
                  />
                </svg>
              </div>
              <h3 className="mt-4 text-lg font-semibold text-gray-900">Error loading subject</h3>
              <p className="mt-2 text-gray-500">{error}</p>
              <button
                onClick={clearError}
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Subject Header */}
      {subject && <SubjectHeader subject={subject} />}

      {/* Subject Actions */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col sm:flex-row gap-3 justify-center sm:justify-start">
            <GroupChatButton
              subjectId={subjectId}
              onError={(error) => console.error('Group chat error:', error)}
            />
            <AttendanceQuickAction
              subjectId={subjectId}
              onSuccess={() => console.log('Attendance marked successfully')}
              onError={(error) => console.error('Attendance error:', error)}
            />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          {/* Students Section */}
          <section>
            <div className="mb-6">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-1 h-8 bg-gradient-to-b from-primary to-primary/60 rounded-full"></div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">
                    Enrolled Students
                  </h2>
                  {!loading && students && (
                    <p className="text-sm text-gray-600 mt-1">
                      {students.length} student{students.length !== 1 ? 's' : ''} enrolled in this subject
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div className="bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200/50 p-6 shadow-sm">
              <StudentGrid
                students={students || []}
                onStartChat={handleStartChat}
                isCreatingChat={creatingChat}
                creatingChatForStudent={creatingChatForStudent || undefined}
                loading={loading}
              />
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}