'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { useAuthStore } from '@/stores/auth-store'
import { useRouter } from 'next/navigation'
import { User, LogOut, Book } from 'lucide-react'
import { User as AuthUser } from '@/types/auth'

export function DashboardHeader() {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [isClient, setIsClient] = useState(false)
  const router = useRouter()

  useEffect(() => {
    setIsClient(true)
    const authState = useAuthStore.getState()
    setUser(authState.user)
  }, [])

  const handleLogout = async () => {
    const { logout } = useAuthStore.getState()
    await logout()
    router.push('/auth/login')
  }

  // Don't render user-specific content during SSR
  if (!isClient) {
    return (
      <header className="bg-white border-b border-gray-200 shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Book className="h-6 w-6 text-primary" />
                <h1 className="text-xl font-semibold text-gray-900">
                  Course Management
                </h1>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </header>
    )
  }

  return (
    <header className="bg-white border-b border-gray-200 shadow-sm">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo and Brand */}
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Book className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-semibold text-gray-900">
                Course Management
              </h1>
            </div>
          </div>

          {/* User Info and Actions */}
          <div className="flex items-center gap-4">
            {user && (
              <>
                {/* User Info */}
                <div className="hidden md:flex items-center gap-3">
                  <div className="flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-lg">
                    <User className="h-4 w-4 text-gray-600" />
                    <div className="text-sm">
                      <p className="font-medium text-gray-900">
                        {user.firstName} {user.lastName}
                      </p>
                      <p className="text-gray-600">{user.email}</p>
                    </div>
                  </div>
                </div>

                {/* Mobile User Initial */}
                <div className="md:hidden flex items-center justify-center w-8 h-8 bg-primary text-white rounded-full text-sm font-medium">
                  {user.firstName?.[0]}{user.lastName?.[0]}
                </div>

                {/* Logout Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLogout}
                  className="flex items-center gap-2"
                >
                  <LogOut className="h-4 w-4" />
                  <span className="hidden sm:inline">Logout</span>
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}