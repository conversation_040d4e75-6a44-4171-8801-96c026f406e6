/**
 * Dashboard constants and configuration
 */

// Attendance thresholds
export const ATTENDANCE_THRESHOLDS = {
  GOOD: 80,
  WARNING: 60,
  POOR: 0,
} as const

// Grid layout breakpoints
export const GRID_BREAKPOINTS = {
  MOBILE: 'grid-cols-1',
  TABLET: 'sm:grid-cols-2',
  DESKTOP: 'lg:grid-cols-3',
  LARGE: 'xl:grid-cols-4',
} as const

// Color schemes for attendance status
export const ATTENDANCE_COLORS = {
  GOOD: {
    text: 'text-green-600',
    background: 'bg-green-50',
    progress: 'bg-green-500',
    border: 'border-green-200',
  },
  WARNING: {
    text: 'text-yellow-600',
    background: 'bg-yellow-50',
    progress: 'bg-yellow-500',
    border: 'border-yellow-200',
  },
  POOR: {
    text: 'text-red-600',
    background: 'bg-red-50',
    progress: 'bg-red-500',
    border: 'border-red-200',
  },
} as const

// Cache durations (in milliseconds)
export const CACHE_DURATIONS = {
  DASHBOARD_DATA: 5 * 60 * 1000, // 5 minutes
  SUBJECT_STATS: 10 * 60 * 1000, // 10 minutes
} as const

// API endpoints
export const API_ENDPOINTS = {
  DASHBOARD: '/api/dashboard',
  SUBJECTS: '/api/dashboard/subjects',
  STATS: '/api/dashboard/stats',
} as const

// Loading states
export const LOADING_COUNTS = {
  SKELETON_CARDS: 6,
  STATS_CARDS: 4,
} as const

// Refresh intervals
export const REFRESH_INTERVALS = {
  AUTO_REFRESH: 30 * 60 * 1000, // 30 minutes
  BACKGROUND_REFRESH: 5 * 60 * 1000, // 5 minutes
} as const

// Navigation routes
export const ROUTES = {
  DASHBOARD: '/dashboard',
  SUBJECT_DETAIL: '/dashboard/subjects',
  SUBJECT_CHAT: '/dashboard/subjects/:id/chat',
  SUBJECT_ATTENDANCE: '/dashboard/subjects/:id/attendance',
} as const

// Feature flags
export const FEATURES = {
  QUICK_CHAT: true,
  MARK_ATTENDANCE: true,
  AUTO_REFRESH: true,
  OFFLINE_SUPPORT: false,
} as const