'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { CheckCircle, Clock, AlertCircle } from 'lucide-react';

interface AttendanceQuickActionProps {
  subjectId: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function AttendanceQuickAction({
  subjectId,
  onSuccess,
  onError
}: AttendanceQuickActionProps) {
  const [isMarking, setIsMarking] = useState(false);
  const [isMarked, setIsMarked] = useState(false);

  const handleMarkAttendance = async () => {
    setIsMarking(true);

    try {
      // TODO: Implement actual attendance marking API call
      // const response = await fetch(`/api/attendance/mark`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     subject_id: subjectId,
      //     date: new Date().toISOString().split('T')[0]
      //   })
      // });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setIsMarked(true);
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error marking attendance:', error);
      if (onError) {
        onError('Failed to mark attendance');
      }
    } finally {
      setIsMarking(false);
    }
  };

  if (isMarked) {
    return (
      <Button
        disabled
        variant="outline"
        className="flex items-center gap-2 border-green-300 text-green-700 bg-green-50"
      >
        <CheckCircle className="h-4 w-4" />
        Attendance Marked
      </Button>
    );
  }

  return (
    <Button
      onClick={handleMarkAttendance}
      disabled={isMarking}
      variant="outline"
      className="flex items-center gap-2 border-green-300 text-green-700 hover:bg-green-50"
    >
      {isMarking ? (
        <>
          <Clock className="h-4 w-4 animate-spin" />
          Marking...
        </>
      ) : (
        <>
          <CheckCircle className="h-4 w-4" />
          Mark Attendance Today
        </>
      )}
    </Button>
  );
}