'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter, useSearchParams } from 'next/navigation';
import { otpVerificationSchema, OTPVerificationFormData } from '@/modules/auth/api/auth-validations';
import { useAuthActions } from '@/stores/auth-store';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';
import { toast } from 'sonner';

interface OTPFormProps {
  email?: string;
}

export function OTPForm({ email: propEmail }: OTPFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const { verifyOTP, resendOTP } = useAuthActions();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get email from props or URL params
  const email = propEmail || searchParams.get('email') || '';

  const form = useForm<OTPVerificationFormData>({
    resolver: zodResolver(otpVerificationSchema),
    defaultValues: {
      otp: '',
    },
  });

  // Countdown timer for resend button
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [timeLeft]);

  // Set initial countdown when component mounts
  useEffect(() => {
    setTimeLeft(60); // 60 seconds initial countdown
  }, []);

  const onSubmit = async (data: OTPVerificationFormData) => {
    if (!email) {
      toast.error('Email is required for verification');
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await verifyOTP(email, data.otp);

      if (result.success) {
        toast.success('Email verified successfully!');
        // Redirect to dashboard after successful verification
        router.push('/dashboard/home');
      } else {
        toast.error(result.error || 'Invalid verification code');
        form.reset(); // Clear the OTP input
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      toast.error('Something went wrong. Please try again.');
      form.reset();
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendOTP = async () => {
    if (!email) {
      toast.error('Email is required to resend verification code');
      return;
    }

    if (timeLeft > 0) {
      toast.error(`Please wait ${timeLeft} seconds before requesting a new code`);
      return;
    }

    setIsResending(true);

    try {
      const result = await resendOTP(email);

      if (result.success) {
        toast.success(result.message || 'Verification code sent successfully!');
        setTimeLeft(60); // Reset countdown
        form.reset(); // Clear the current OTP input
      } else {
        toast.error(result.error || 'Failed to resend verification code');
      }
    } catch (error) {
      console.error('Resend OTP error:', error);
      toast.error('Something went wrong. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  if (!email) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl">Email Required</CardTitle>
          <CardDescription>
            Please provide an email address to verify your account.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            onClick={() => router.push('/auth/signup')}
            className="w-full"
          >
            Go to Sign Up
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl">Verify your email</CardTitle>
        <CardDescription>
          We've sent a 6-digit verification code to<br />
          <strong>{email}</strong>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="otp"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel className="text-center block">Verification Code</FormLabel>
                  <FormControl>
                    <div className="flex justify-center">
                      <InputOTP maxLength={6} {...field}>
                        <InputOTPGroup>
                          <InputOTPSlot index={0} />
                          <InputOTPSlot index={1} />
                          <InputOTPSlot index={2} />
                          <InputOTPSlot index={3} />
                          <InputOTPSlot index={4} />
                          <InputOTPSlot index={5} />
                        </InputOTPGroup>
                      </InputOTP>
                    </div>
                  </FormControl>
                  <FormMessage className="text-center" />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              className="w-full"
              disabled={isSubmitting || form.watch('otp').length !== 6}
            >
              {isSubmitting ? 'Verifying...' : 'Verify Email'}
            </Button>
          </form>
        </Form>

        <div className="mt-6 text-center space-y-2">
          <p className="text-sm text-muted-foreground">
            Didn't receive the code?
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={handleResendOTP}
            disabled={isResending || timeLeft > 0}
          >
            {isResending
              ? 'Sending...'
              : timeLeft > 0
              ? `Resend in ${timeLeft}s`
              : 'Resend Code'
            }
          </Button>
        </div>

        <div className="mt-4 text-center text-sm text-muted-foreground">
          <p>Check your spam folder if you don't see the email.</p>
          <p className="mt-1">The code expires in 10 minutes.</p>
        </div>
      </CardContent>
    </Card>
  );
}