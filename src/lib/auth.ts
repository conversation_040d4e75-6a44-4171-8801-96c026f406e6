import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { addMinutes, isAfter } from 'date-fns';

export interface JWTPayload {
  userId: string;
  email: string;
  verified: boolean;
  iat?: number;
  exp?: number;
}

export const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
export const JWT_EXPIRES_IN = '15m';
export const REFRESH_TOKEN_EXPIRES_IN = '7d';

/**
 * Hash a password using bcrypt
 */
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

/**
 * Verify a password against its hash
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

/**
 * Generate a JWT access token
 */
export function generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
}

/**
 * Generate a JWT refresh token
 */
export function generateRefreshToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: REFRESH_TOKEN_EXPIRES_IN });
}

/**
 * Verify and decode a JWT token
 */
export function verifyToken(token: string): JWTPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as JWTPayload;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

/**
 * Generate a 6-digit OTP
 */
export function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * Generate OTP with expiry (10 minutes)
 */
export function generateOTPWithExpiry() {
  const otp = generateOTP();
  const now = new Date();
  const expiresAt = addMinutes(now, 10); // 10 minutes from now using date-fns

  return { otp, expiresAt };
}

/**
 * Verify if OTP is still valid
 */
export function isOTPValid(expiresAt: Date): boolean {
  const now = new Date();
  return !isAfter(now, expiresAt); // Using date-fns for timezone-safe comparison
}

/**
 * Generate a secure random token for password reset
 */
export function generateResetToken(): string {
  return jwt.sign(
    {
      type: 'reset',
      timestamp: Date.now()
    },
    JWT_SECRET,
    { expiresIn: '1h' }
  );
}

/**
 * Verify password reset token
 */
export function verifyResetToken(token: string): { valid: boolean; expired?: boolean } {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    if (decoded.type !== 'reset') {
      return { valid: false };
    }
    return { valid: true };
  } catch (error: any) {
    if (error.name === 'TokenExpiredError') {
      return { valid: false, expired: true };
    }
    return { valid: false };
  }
}