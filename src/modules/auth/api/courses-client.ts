import { createClient } from '@/lib/supabase/client';
import { Course } from '@/types/auth';

/**
 * Fetch all courses from Supabase (client-side)
 */
export async function getCourses(): Promise<{ courses: Course[] | null; error: string | null }> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from('courses')
      .select('id, name, code, department')
      .order('name');

    if (error) {
      console.error('Error fetching courses:', error);
      return { courses: null, error: 'Failed to fetch courses' };
    }

    return { courses: data as Course[], error: null };
  } catch (error) {
    console.error('Client error fetching courses:', error);
    return { courses: null, error: 'Failed to fetch courses' };
  }
}


/**
 * Get course by ID (client-side)
 */
export async function getCourseById(id: number): Promise<{ course: Course | null; error: string | null }> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from('courses')
      .select('id, name, code, department')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching course by ID:', error);
      return { course: null, error: 'Failed to fetch course' };
    }

    return { course: data as Course, error: null };
  } catch (error) {
    console.error('Client error fetching course by ID:', error);
    return { course: null, error: 'Failed to fetch course' };
  }
}

