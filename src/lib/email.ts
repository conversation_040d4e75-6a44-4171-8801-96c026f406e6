// Import only what we need to avoid React Email dependency
const resendUrl = 'https://api.resend.com/emails';

export interface EmailResponse {
  success: boolean;
  data?: any;
  error?: any;
}

/**
 * Get the OTP email template with dynamic content
 */
function getOTPEmailTemplate(otp: string): string {
  return `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html dir="ltr" lang="en">
  <head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
    <meta name="x-apple-disable-message-reformatting" />
  </head>
  <body style="background-color:#f8fafc;margin:0;padding:0">
    <table
      border="0"
      width="100%"
      cellpadding="0"
      cellspacing="0"
      role="presentation"
      align="center"
      style="margin:0;padding:0;width:100%">
      <tbody>
        <tr>
          <td
            style='background-color:#f8fafc;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;text-align:center;padding:0;margin:0'>
            <div
              style="display:none;overflow:hidden;line-height:1px;opacity:0;max-height:0;max-width:0"
              data-skip-in-text="true">
              Your verification code for Course Management.
            </div>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="max-width:37.5em;margin:40px auto;padding:40px 32px;background-color:#ffffff;border-radius:12px;box-shadow:0 4px 6px -1px rgba(0, 0, 0, 0.1)">
              <tbody>
                <tr style="width:100%">
                  <td>
                    <div style="text-align:center;margin-bottom:32px">
                      <h1 style="font-size:24px;font-weight:600;color:#1e40af;margin:0;letter-spacing:-0.025em">
                        Course Management
                      </h1>
                    </div>

                    <h2 style="font-size:24px;font-weight:600;margin-top:0;margin-bottom:24px;color:#1f2937">
                      Verify your email address
                    </h2>

                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="margin:32px 0">
                      <tbody>
                        <tr>
                          <td>
                            <p style="font-size:16px;line-height:24px;margin-top:0;margin-bottom:24px;color:#4b5563">
                              Thanks for starting the Course Management registration process. We want to make sure it's really you. Please enter the following verification code when prompted.
                            </p>

                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="margin:32px 0;text-align:center">
                              <tbody>
                                <tr>
                                  <td>
                                    <p style="font-size:14px;line-height:20px;margin:0;margin-bottom:8px;color:#6b7280;font-weight:600">
                                      Verification Code
                                    </p>
                                    <div style="background-color:#f8fafc;border:2px solid #e5e7eb;border-radius:8px;padding:20px;margin:16px 0;display:inline-block">
                                      <p style="font-size:32px;line-height:1;margin:0;font-weight:700;color:#1f2937;letter-spacing:0.1em;font-family:Monaco,Menlo,Consolas,'Courier New',monospace">
                                        ${otp}
                                      </p>
                                    </div>
                                    <p style="font-size:14px;line-height:20px;margin:8px 0 0 0;color:#9ca3af">
                                      (This code is valid for 10 minutes)
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>

                            <p style="font-size:14px;line-height:20px;margin-top:32px;margin-bottom:16px;color:#6b7280">
                              If you didn't request this verification code, please ignore this email.
                            </p>
                          </td>
                        </tr>
                      </tbody>
                    </table>

                    <p style="font-size:16px;line-height:24px;margin-top:32px;margin-bottom:0;color:#4b5563">
                      Best regards,<br />
                      The Course Management Team
                    </p>

                    <hr style="width:100%;border:none;border-top:1px solid #e5e7eb;margin-top:40px;margin-bottom:24px" />

                    <p style="font-size:12px;line-height:16px;color:#9ca3af;margin:0;text-align:center">
                      Course Management Platform
                    </p>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </body>
</html>
  `;
}

/**
 * Send OTP verification email
 */
export async function sendOTPEmail(
  email: string,
  otp: string,
  firstName?: string
): Promise<EmailResponse> {
  try {
    if (!process.env.RESEND_API_KEY) {
      console.error('RESEND_API_KEY not configured');
      return { success: false, error: 'Email service not configured' };
    }

    const response = await fetch(resendUrl, {
       method: 'POST',
       headers: {
          Authorization: `Bearer ${process.env.RESEND_API_KEY}`,
          'Content-Type': 'application/json',
       },
       body: JSON.stringify({
          from: 'Course Management <<EMAIL>>',
          to: [email],
          subject: 'Verify Your Email - Course Management',
          html: getOTPEmailTemplate(otp),
       }),
    });

    const data = await response.json();

    if (!response.ok) {
      console.error('Email sending error:', data);
      return { success: false, error: data };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Email sending failed:', error);
    return { success: false, error };
  }
}

/**
 * Send password reset email
 */
export async function sendPasswordResetEmail(
  email: string,
  resetToken: string,
  firstName?: string
): Promise<EmailResponse> {
  try {
    if (!process.env.RESEND_API_KEY) {
      console.error('RESEND_API_KEY not configured');
      return { success: false, error: 'Email service not configured' };
    }

    const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/auth/reset-password?token=${resetToken}`;

    const response = await fetch(resendUrl, {
       method: 'POST',
       headers: {
          Authorization: `Bearer ${process.env.RESEND_API_KEY}`,
          'Content-Type': 'application/json',
       },
       body: JSON.stringify({
          from: 'Course Management <<EMAIL>>',
          to: [email],
          subject: 'Reset Your Password - Course Management',
          html: `
          <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
            <h2>Reset Your Password</h2>
            <p>Hello ${firstName || 'there'},</p>
            <p>You requested to reset your password for Course Management. Click the button below to reset your password:</p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}" style="background-color: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Reset Password
              </a>
            </div>

            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #666;">${resetUrl}</p>

            <p>This link will expire in 1 hour for security reasons.</p>
            <p>If you didn't request this password reset, please ignore this email.</p>

            <hr style="margin: 30px 0;">
            <p style="color: #666; font-size: 12px;">
              Course Management - Student Communication System
            </p>
          </div>
        `,
       }),
    });

    const data = await response.json();

    if (!response.ok) {
      console.error('Email sending error:', data);
      return { success: false, error: data };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Email sending failed:', error);
    return { success: false, error };
  }
}

/**
 * Send welcome email after successful registration
 */
export async function sendWelcomeEmail(
  email: string,
  firstName: string,
  course?: string
): Promise<EmailResponse> {
  try {
    if (!process.env.RESEND_API_KEY) {
      console.error('RESEND_API_KEY not configured');
      return { success: false, error: 'Email service not configured' };
    }

    const response = await fetch(resendUrl, {
       method: 'POST',
       headers: {
          Authorization: `Bearer ${process.env.RESEND_API_KEY}`,
          'Content-Type': 'application/json',
       },
       body: JSON.stringify({
          from: 'Course Management <<EMAIL>>',
          to: [email],
          subject: 'Welcome to Course Management!',
          html: `
          <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
            <h2>Welcome to Course Management!</h2>
            <p>Hello ${firstName},</p>
            <p>Your email has been verified successfully and your account is now active!</p>

            ${course ? `<p>You've been enrolled in: <strong>${course}</strong></p>` : ''}

            <p>You can now:</p>
            <ul>
              <li>View your subjects and classmates</li>
              <li>Join group chats for your subjects</li>
              <li>Mark your attendance</li>
              <li>Connect with your peers</li>
            </ul>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${
                 process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
              }/dashboard" style="background-color: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Go to Dashboard
              </a>
            </div>

            <p>If you have any questions, feel free to reach out to our support team.</p>

            <hr style="margin: 30px 0;">
            <p style="color: #666; font-size: 12px;">
              Course Management - Student Communication System
            </p>
          </div>
        `,
       }),
    });

    const data = await response.json();

    if (!response.ok) {
      console.error('Email sending error:', data);
      return { success: false, error: data };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Email sending failed:', error);
    return { success: false, error };
  }
}