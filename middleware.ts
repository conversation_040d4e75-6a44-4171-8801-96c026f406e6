import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/auth/login',
    '/auth/sign-up',
    '/auth/verify-otp',
    '/auth/forgot-password',
    '/auth/reset-password',
    '/api/auth/signup',
    '/api/auth/login',
    '/api/auth/verify-otp',
    '/api/auth/resend-otp',
    '/api/auth/forgot-password',
    '/api/auth/reset-password',
  ];

  // Check if current path is public
  const isPublicRoute = publicRoutes.some(route => {
    if (route.includes('[') || route.includes('*')) {
      // Handle dynamic routes
      const routePattern = route.replace(/\[.*?\]/g, '.*');
      return new RegExp(`^${routePattern}$`).test(pathname);
    }
    return pathname === route || pathname.startsWith(route + '/');
  });

  // If it's a public route, allow access
  if (isPublicRoute) {
    return NextResponse.next();
  }

  // For protected routes, check authentication
  const accessToken = request.cookies.get('accessToken')?.value;

  if (!accessToken) {
    // No token, redirect to login
    const loginUrl = new URL('/auth/login', request.url);
    return NextResponse.redirect(loginUrl);
  }

  // Verify the token
  const tokenPayload = verifyToken(accessToken);

  if (!tokenPayload) {
    // Invalid token, redirect to login and clear cookies
    const response = NextResponse.redirect(new URL('/auth/login', request.url));
    response.cookies.delete('accessToken');
    response.cookies.delete('refreshToken');
    return response;
  }

  // Check if user is verified for certain routes
  if (pathname.startsWith('/dashboard') && !tokenPayload.verified) {
    const verifyUrl = new URL('/auth/verify-otp', request.url);
    return NextResponse.redirect(verifyUrl);
  }

  // Token is valid, continue
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};