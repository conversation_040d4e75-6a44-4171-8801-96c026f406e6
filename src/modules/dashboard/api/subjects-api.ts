import { createClient } from '@/lib/supabase/server'
import { DashboardData } from '../types/dashboard.types'
import { EnrolledSubject } from '../types/subject.types'

/**
 * Get user's enrolled subjects with attendance stats
 */
export async function getEnrolledSubjects(userId: string): Promise<{ data: DashboardData | null; error: string | null }> {
  try {
    const supabase = await createClient()

    // Get user's enrolled subjects with attendance data
    const { data: subjectsData, error: subjectsError } = await supabase
      .from('subjects')
      .select(`
        id,
        name,
        code,
        description,
        user_subjects!inner(
          enrolled_at,
          user_id
        )
      `)
      .eq('user_subjects.user_id', userId)
      .eq('user_subjects.is_active', true)

    if (subjectsError) {
      console.error('Error fetching subjects:', subjectsError)
      return { data: null, error: 'Failed to load subjects' }
    }

    if (!subjectsData || subjectsData.length === 0) {
      return {
        data: {
          subjects: [],
          overall_stats: {
            total_subjects: 0,
            avg_attendance: 0,
            total_classes_attended: 0
          }
        },
        error: null
      }
    }

    // Get attendance data for all user's subjects
    const subjectIds = subjectsData.map(s => s.id)
    const { data: attendanceData, error: attendanceError } = await supabase
      .from('attendance_records')
      .select('subject_id, date')
      .eq('user_id', userId)
      .in('subject_id', subjectIds)

    if (attendanceError) {
      console.error('Error fetching attendance:', attendanceError)
      // Continue without attendance data rather than failing completely
    }

    // Process subjects and calculate stats
    const enrolledSubjects: EnrolledSubject[] = subjectsData.map(subject => {
      // Get attendance for this subject
      const subjectAttendance = attendanceData?.filter(a => a.subject_id === subject.id) || []

      // For now, we'll calculate based on days enrolled (mock total classes)
      const enrolledDate = new Date(subject.user_subjects[0].enrolled_at)
      const daysSinceEnrollment = Math.floor((Date.now() - enrolledDate.getTime()) / (1000 * 60 * 60 * 24))
      const estimatedTotalClasses = Math.max(1, Math.floor(daysSinceEnrollment / 7) * 3) // Assume 3 classes per week

      const attendedClasses = subjectAttendance.length
      const attendancePercentage = estimatedTotalClasses > 0 ?
        Math.round((attendedClasses / estimatedTotalClasses) * 100 * 10) / 10 : 0

      // Get last attendance date
      const lastAttendance = subjectAttendance.length > 0 ?
        subjectAttendance.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0].date :
        null

      return {
        id: subject.id.toString(),
        name: subject.name,
        code: subject.code || 'N/A',
        description: subject.description || '',
        instructor: 'Instructor', // TODO: Add instructor field to subjects table
        enrolled_at: subject.user_subjects[0].enrolled_at,
        stats: {
          total_classes: estimatedTotalClasses,
          attended_classes: attendedClasses,
          attendance_percentage: attendancePercentage,
          last_attendance: lastAttendance
        }
      }
    })

    // Calculate overall stats
    const totalSubjects = enrolledSubjects.length
    const totalClassesAttended = enrolledSubjects.reduce((sum, subject) => sum + subject.stats.attended_classes, 0)
    const avgAttendance = totalSubjects > 0 ?
      enrolledSubjects.reduce((sum, subject) => sum + subject.stats.attendance_percentage, 0) / totalSubjects : 0

    const dashboardData: DashboardData = {
      subjects: enrolledSubjects,
      overall_stats: {
        total_subjects: totalSubjects,
        avg_attendance: Math.round(avgAttendance * 10) / 10,
        total_classes_attended: totalClassesAttended
      }
    }

    return { data: dashboardData, error: null }

  } catch (error) {
    console.error('Database error getting enrolled subjects:', error)
    return { data: null, error: 'Failed to load subjects' }
  }
}

/**
 * Get subject statistics for a specific subject
 */
export async function getSubjectStats(userId: string, subjectId: string) {
  try {
    const supabase = await createClient()

    // TODO: Implement actual database query for subject stats
    // const { data, error } = await supabase
    //   .from('attendance')
    //   .select('status, date')
    //   .eq('user_id', userId)
    //   .eq('subject_id', subjectId)

    return { data: null, error: 'Not implemented yet' }
  } catch (error) {
    console.error('Database error getting subject stats:', error)
    return { data: null, error: 'Failed to load subject statistics' }
  }
}