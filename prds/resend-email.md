// lib/email.ts
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function sendOTPEmail(email: string, otp: string, firstName: string) {
  try {
    const { data, error } = await resend.emails.send({
      from: 'College Platform <<EMAIL>>', // Replace with your domain
      to: [email],
      subject: 'Verify Your Email - College Platform',
      html: getOTPEmailTemplate(otp, firstName), // Your existing HTML template
    });

    if (error) {
      console.error('Email sending error:', error);
      return { success: false, error };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Email sending failed:', error);
    return { success: false, error };
  }
}

function getOTPEmailTemplate(otp: string, firstName: string): string {
  // Use your existing HTML template here
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Verify Your Email</title>
    </head>
    <body>
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <h2>Welcome ${firstName}!</h2>
        <p>Thank you for signing up for College Platform. Please verify your email address using the OTP below:</p>
        
        <div style="background: #f5f5f5; padding: 20px; text-align: center; margin: 20px 0;">
          <h1 style="font-size: 32px; letter-spacing: 8px; margin: 0; color: #333;">
            ${otp}
          </h1>
        </div>
        
        <p>This OTP will expire in 10 minutes for security reasons.</p>
        <p>If you didn't request this, please ignore this email.</p>
        
        <hr style="margin: 30px 0;">
        <p style="color: #666; font-size: 12px;">
          College Platform - Student Communication System
        </p>
      </div>
    </body>
    </html>
  `;
}

// Helper function to generate 6-digit OTP
export function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Environment variables needed in .env.local:
// RESEND_API_KEY=your_resend_api_key_here