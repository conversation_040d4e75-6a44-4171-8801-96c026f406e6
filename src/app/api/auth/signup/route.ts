import { NextRequest, NextResponse } from 'next/server';
import { apiSignupSchema } from '@/modules/auth/api/auth-validations';
import { createUser, createOTPRecord } from '@/modules/auth/api/auth-database-server';
import { generateOTPWithExpiry } from '@/lib/auth';
import { sendOTPEmail } from '@/lib/email';

export async function POST(request: NextRequest) {
   try {
      const body = await request.json();

      // Validate request body
      const validationResult = apiSignupSchema.safeParse(body);
      if (!validationResult.success) {
         return NextResponse.json(
            {
               success: false,
               error: 'Invalid input data',
               details: validationResult.error.issues,
            },
            { status: 400 }
         );
      }

      const { firstName, lastName, email, password, courseId } = validationResult.data;

      // Create user in database
      const { user, error: createUserError } = await createUser({
         firstName,
         lastName,
         email,
         password,
         courseId,
      });

      if (createUserError) {
         return NextResponse.json(
            {
               success: false,
               error: createUserError,
            },
            { status: 400 }
         );
      }

      if (!user) {
         return NextResponse.json(
            {
               success: false,
               error: 'Failed to create user account',
            },
            { status: 500 }
         );
      }

      // Generate OTP and expiry
      const { otp, expiresAt } = generateOTPWithExpiry();

      // Store OTP in database
      const { success: otpStored, error: otpError } = await createOTPRecord(email, otp, expiresAt);

      if (!otpStored) {
         console.error('Failed to store OTP:', otpError);
         return NextResponse.json(
            {
               success: false,
               error: 'Failed to generate verification code',
            },
            { status: 500 }
         );
      }

      // Send OTP email
      const emailResult = await sendOTPEmail(email, otp, firstName);

      if (!emailResult.success) {
         console.error('Failed to send OTP email:', emailResult.error);
         return NextResponse.json(
            {
               success: false,
               error: 'Failed to send verification email',
            },
            { status: 500 }
         );
      }

      return NextResponse.json(
         {
            success: true,
            message:
               'Account created successfully. Please check your email for the verification code.',
            requiresVerification: true,
         },
         { status: 201 }
      );
   } catch (error) {
      console.error('Signup error:', error);
      return NextResponse.json(
         {
            success: false,
            error: 'Internal server error',
         },
         { status: 500 }
      );
   }
}