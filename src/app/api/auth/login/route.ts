import { NextRequest, NextResponse } from 'next/server';
import { apiLoginSchema } from '@/modules/auth/api/auth-validations';
import { findUserByEmail, getUserPasswordHash } from '@/modules/auth/api/auth-database-server';
import { verifyPassword, generateAccessToken, generateRefreshToken } from '@/lib/auth';
import { AuthSession } from '@/types/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validationResult = apiLoginSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const { email, password } = validationResult.data;

    // Get user by email
    const { user, error: userError } = await findUserByEmail(email);

    if (userError || !user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email or password',
        },
        { status: 401 }
      );
    }

    // Check if email is verified
    if (!user.emailVerified) {
      return NextResponse.json(
        {
          success: false,
          error: 'Please verify your email before logging in',
          requiresVerification: true,
        },
        { status: 401 }
      );
    }

    // Get password hash
    const { passwordHash, error: passwordError } = await getUserPasswordHash(email);

    if (passwordError || !passwordHash) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication failed',
        },
        { status: 500 }
      );
    }

    // Verify password
    const passwordValid = await verifyPassword(password, passwordHash);

    if (!passwordValid) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email or password',
        },
        { status: 401 }
      );
    }

    // Generate tokens
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      verified: user.emailVerified,
    };

    const accessToken = generateAccessToken(tokenPayload);
    const refreshToken = generateRefreshToken(tokenPayload);

    // Create session
    const session: AuthSession = {
      user,
      accessToken,
      refreshToken,
      expiresAt: Date.now() + (15 * 60 * 1000), // 15 minutes from now
    };

    // Set HTTP-only cookies for tokens
    const response = NextResponse.json(
      {
        success: true,
        message: 'Login successful',
        user,
        session,
      },
      { status: 200 }
    );

    // Set secure cookies
    response.cookies.set('accessToken', accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 15 * 60, // 15 minutes
      path: '/',
    });

    response.cookies.set('refreshToken', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/',
    });

    return response;
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}