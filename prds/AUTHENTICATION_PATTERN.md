# API Authentication Pattern

## Quick Start

```typescript
import { withAuth } from '@/lib/api-auth'
import { yourModuleApi } from '@/modules/your-module/api'

export const GET = withAuth(async (request, { user }) => {
  const { data, error } = await yourModule<PERSON><PERSON>(user.userId)

  if (error) {
    return NextResponse.json({ error }, { status: 500 })
  }

  return NextResponse.json(data)
})
```

## Protected vs Public

```typescript
// Protected route - authenticated + verified users only
export const GET = withAuth(async (request, { user }) => {
  // user is guaranteed to be authenticated AND verified
})

// Public route - no auth needed
export async function POST(request: NextRequest) {
  // Anyone can access this (login, signup, etc.)
}
```

## Available Data

```typescript
export const GET = withAuth(async (request, { user }) => {
  // user.userId    - string
  // user.email     - string
  // user.verified  - boolean (always true)
})
```

## Rules

1. **Use `withAuth`** for protected routes only
2. **No wrapper** for public routes (auth endpoints, public data)
3. **Import from module APIs** directly - no abstractions
4. **Handle errors consistently** with try/catch and status codes