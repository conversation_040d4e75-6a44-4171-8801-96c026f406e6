import { NextRequest, NextResponse } from 'next/server';
import { apiResetPasswordSchema } from '@/modules/auth/api/auth-validations';
import { verifyAndUseResetToken, updateUserPassword } from '@/modules/auth/api/auth-database-server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validationResult = apiResetPasswordSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const { token, password } = validationResult.data;

    // Verify and use reset token
    const { valid: tokenValid, email, error: tokenError } = await verifyAndUseResetToken(token);

    if (!tokenValid || !email) {
      return NextResponse.json(
        {
          success: false,
          error: tokenError || 'Invalid or expired reset token',
        },
        { status: 400 }
      );
    }

    // Update user password
    const { success: passwordUpdated, error: passwordError } = await updateUserPassword(email, password);

    if (!passwordUpdated) {
      console.error('Failed to update password:', passwordError);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to update password',
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        success: true,
        message: 'Password reset successfully. You can now login with your new password.',
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Reset password error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}