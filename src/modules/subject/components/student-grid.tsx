'use client';

import { EnrolledStudent } from '../types';
import { StudentCard } from './student-card';
import { StudentListSkeleton } from './student-list-skeleton';
import { useAuthStore } from '@/stores/auth-store';

interface StudentGridProps {
   students: EnrolledStudent[];
   onStartChat: (studentId: string) => void;
   isCreatingChat?: boolean;
   creatingChatForStudent?: string;
   loading?: boolean;
}

export function StudentGrid({
   students,
   onStartChat,
   isCreatingChat = false,
   creatingChatForStudent,
   loading = false,
}: StudentGridProps) {
   const user = useAuthStore(state => state.user);

   if (loading) {
      return <StudentListSkeleton />;
   }

   if (students.length === 0) {
      return (
         <div className='text-center py-16'>
            <div className='mx-auto max-w-md'>
               <div className='mx-auto h-24 w-24 text-gray-300 mb-6'>
                  <div className='w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center'>
                     <svg
                        fill='none'
                        viewBox='0 0 24 24'
                        strokeWidth={1.5}
                        stroke='currentColor'
                        className='w-12 h-12'
                     >
                        <path
                           strokeLinecap='round'
                           strokeLinejoin='round'
                           d='M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z'
                        />
                     </svg>
                  </div>
               </div>
               <h3 className='text-xl font-bold text-gray-900 mb-2'>No students found</h3>
               <p className='text-gray-500 leading-relaxed'>
                  There are no students enrolled in this subject yet. Students will appear here once they enroll.
               </p>
            </div>
         </div>
      );
   }

   return (
      <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
         {students.map(student => (
            <StudentCard
               key={student.id}
               student={student}
               onStartChat={onStartChat}
               isCreatingChat={isCreatingChat && creatingChatForStudent === student.id}
               isCurrentUser={String(user?.id) === String(student.id)}
            />
         ))}
      </div>
   );
}
