import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/api-auth';
import { getOrCreateDirectChat } from '@/modules/subject/api/conversation-server-api';
import { createChatSchema } from '@/modules/subject/schemas';

export const POST = withAuth(async (request: NextRequest, { user }) => {
  try {
    const body = await request.json();

    // Validate request body
    const validation = createChatSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validation.error.issues },
        { status: 400 }
      );
    }

    const { participant_id } = validation.data;

    // Prevent creating chat with self
    if (participant_id === user.userId) {
      return NextResponse.json(
        { error: 'Cannot create chat with yourself' },
        { status: 400 }
      );
    }

    // Create or get existing direct chat
    const { data, error } = await getOrCreateDirectChat(user.userId, participant_id);

    if (error) {
      return NextResponse.json({ error }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Create direct chat API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});