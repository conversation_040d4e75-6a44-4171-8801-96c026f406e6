import { useAuth, useAuthStore } from '@/stores/auth-store';
import axios from 'axios';

const apiClient = axios.create({
   baseURL: '/api',
   headers: {
      'Content-Type': 'application/json',
   },
});

// Request interceptor to add auth token if available
apiClient.interceptors.request.use(
   config => {
      // Get token from localStorage (Zustand persist storage)
      if (typeof window !== 'undefined') {
         const token = useAuthStore.getState().session?.accessToken;

         try {
            if (token) {
               config.headers.Authorization = `Bearer ${token}`;
            }
         } catch (error) {
            console.error('Error parsing auth storage:', error);
         }
      }
      return config;
   },
   error => {
      return Promise.reject(error);
   }
);

// Response interceptor for centralized error handling
apiClient.interceptors.response.use(
   response => {
      return response;
   },
   error => {
      // Handle common HTTP errors here if needed
      return Promise.reject(error);
   }
);

export default apiClient;
