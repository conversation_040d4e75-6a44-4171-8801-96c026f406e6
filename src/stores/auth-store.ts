'use client';

import { verifyToken } from '@/lib/auth';
import apiClient from '@/lib/axios';
import { AuthResponse, AuthStore, SignupData, User } from '@/types/auth';
import React from 'react';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';

/**
 * Main auth store using Zustand with persistence
 */
export const useAuthStore = create<AuthStore>()(
   persist(
      (set, get) => ({
         // Initial state
         user: null,
         session: null,
         isLoading: false,
         isAuthenticated: false,

         // Actions
         login: async (email: string, password: string): Promise<AuthResponse> => {
            set({ isLoading: true });

            try {
               const response = await apiClient.post('/auth/login', { email, password });
               const data = response.data;

               if (data.success && data.session) {
                  set({
                     user: data.user,
                     session: data.session,
                     isAuthenticated: true,
                     isLoading: false,
                  });
                  return { success: true, user: data.user, session: data.session };
               } else {
                  set({ isLoading: false });
                  return { success: false, error: data.error || 'Login failed' };
               }
            } catch (error: any) {
               set({ isLoading: false });
               return {
                  success: false,
                  error: error.response?.data?.error || 'Network error occurred',
               };
            }
         },

         signup: async (data: SignupData): Promise<AuthResponse> => {
            set({ isLoading: true });

            try {
               const response = await apiClient.post('/auth/signup', data);
               const result = response.data;

               set({ isLoading: false });

               if (result.success) {
                  return {
                     success: true,
                     message: result.message,
                     requiresVerification: true,
                  };
               } else {
                  return { success: false, error: result.error || 'Signup failed' };
               }
            } catch (error: any) {
               set({ isLoading: false });
               return {
                  success: false,
                  error: error.response?.data?.error || 'Network error occurred',
               };
            }
         },

         verifyOTP: async (email: string, otp: string): Promise<AuthResponse> => {
            set({ isLoading: true });

            try {
               const response = await apiClient.post('/auth/verify-otp', { email, otp });
               const data = response.data;

               if (data.success && data.session) {
                  set({
                     user: data.user,
                     session: data.session,
                     isAuthenticated: true,
                     isLoading: false,
                  });
                  return { success: true, user: data.user, session: data.session };
               } else {
                  set({ isLoading: false });
                  return { success: false, error: data.error || 'OTP verification failed' };
               }
            } catch (error: any) {
               set({ isLoading: false });
               return {
                  success: false,
                  error: error.response?.data?.error || 'Network error occurred',
               };
            }
         },

         resendOTP: async (email: string): Promise<AuthResponse> => {
            set({ isLoading: true });

            try {
               const response = await apiClient.post('/auth/resend-otp', { email });
               const data = response.data;
               set({ isLoading: false });

               if (data.success) {
                  return { success: true, message: data.message };
               } else {
                  return { success: false, error: data.error || 'Failed to resend OTP' };
               }
            } catch (error: any) {
               set({ isLoading: false });
               return {
                  success: false,
                  error: error.response?.data?.error || 'Network error occurred',
               };
            }
         },

         logout: async (): Promise<void> => {
            try {
               // Call logout API to invalidate server-side session
               await apiClient.post('/auth/logout');
            } catch (error) {
               console.error('Logout API call failed:', error);
            }

            // Clear local state regardless of API call result
            set({
               user: null,
               session: null,
               isAuthenticated: false,
               isLoading: false,
            });
         },

         forgotPassword: async (email: string): Promise<AuthResponse> => {
            set({ isLoading: true });

            try {
               const response = await apiClient.post('/auth/forgot-password', { email });
               const data = response.data;
               set({ isLoading: false });

               if (data.success) {
                  return { success: true, message: data.message };
               } else {
                  return { success: false, error: data.error || 'Failed to send reset email' };
               }
            } catch (error: any) {
               set({ isLoading: false });
               return {
                  success: false,
                  error: error.response?.data?.error || 'Network error occurred',
               };
            }
         },

         resetPassword: async (token: string, password: string): Promise<AuthResponse> => {
            set({ isLoading: true });

            try {
               const response = await apiClient.post('/auth/reset-password', { token, password });
               const data = response.data;
               set({ isLoading: false });

               if (data.success) {
                  return { success: true, message: data.message };
               } else {
                  return { success: false, error: data.error || 'Failed to reset password' };
               }
            } catch (error: any) {
               set({ isLoading: false });
               return {
                  success: false,
                  error: error.response?.data?.error || 'Network error occurred',
               };
            }
         },

         refreshToken: async (): Promise<boolean> => {
            const { session } = get();

            if (!session?.refreshToken) {
               return false;
            }

            try {
               const response = await apiClient.post('/auth/refresh', {
                  refreshToken: session.refreshToken,
               });
               const data = response.data;

               if (data.success && data.session) {
                  set({
                     session: data.session,
                     user: data.user,
                     isAuthenticated: true,
                  });
                  return true;
               } else {
                  // Refresh failed, logout user
                  get().logout();
                  return false;
               }
            } catch (error) {
               console.error('Token refresh failed:', error);
               get().logout();
               return false;
            }
         },

         initializeAuth: async (): Promise<void> => {
            const { session } = get();

            if (!session?.accessToken) {
               set({ isLoading: false });
               return;
            }

            // Verify current token
            const tokenPayload = verifyToken(session.accessToken);

            if (!tokenPayload) {
               // Token is invalid, try to refresh
               const refreshSuccess = await get().refreshToken();
               if (!refreshSuccess) {
                  set({ isLoading: false });
                  return;
               }
            }

            // Check if token is about to expire (less than 5 minutes left)
            if (tokenPayload && tokenPayload.exp) {
               const timeUntilExpiry = tokenPayload.exp * 1000 - Date.now();
               if (timeUntilExpiry < 5 * 60 * 1000) {
                  // Less than 5 minutes
                  await get().refreshToken();
               }
            }

            set({ isLoading: false });
         },

         updateUser: (userData: Partial<User>): void => {
            const { user } = get();
            if (user) {
               set({
                  user: { ...user, ...userData },
               });
            }
         },
      }),
      {
         name: 'auth-storage',
         partialize: state => ({
            user: state.user,
            session: state.session,
            isAuthenticated: state.isAuthenticated,
         }),
      }
   )
);

/**
 * Hook to initialize auth on app start
 */
export const useInitializeAuth = () => {
   const initializeAuth = useAuthStore(state => state.initializeAuth);
   const isLoading = useAuthStore(state => state.isLoading);

   React.useEffect(() => {
      initializeAuth();
   }, [initializeAuth]);

   return { isLoading };
};

export const useAuth = () => {
   return useAuthStore.getState();
};

export const useAuthActions = () => {
   return React.useMemo(() => {
      const state = useAuthStore.getState();
      return {
         login: state.login,
         signup: state.signup,
         verifyOTP: state.verifyOTP,
         resendOTP: state.resendOTP,
         logout: state.logout,
         forgotPassword: state.forgotPassword,
         resetPassword: state.resetPassword,
         refreshToken: state.refreshToken,
         updateUser: state.updateUser,
      };
   }, []);
};

/**
 * Auto token refresh setup
 */
export const setupTokenRefresh = () => {
   const refreshToken = useAuthStore.getState().refreshToken;
   const session = useAuthStore.getState().session;

   if (!session?.accessToken) return;

   const tokenPayload = verifyToken(session.accessToken);
   if (!tokenPayload?.exp) return;

   const timeUntilExpiry = tokenPayload.exp * 1000 - Date.now();
   const refreshTime = Math.max(timeUntilExpiry - 5 * 60 * 1000, 0); // Refresh 5 minutes before expiry

   setTimeout(() => {
      refreshToken().then(success => {
         if (success) {
            setupTokenRefresh(); // Setup next refresh
         }
      });
   }, refreshTime);
};
