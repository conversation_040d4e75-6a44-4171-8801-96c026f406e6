import { SubjectDetails, EnrolledStudent, GetSubjectResponse } from '../types';
import apiClient from '@/lib/axios';

/**
 * Client-side API functions for subject operations
 */
export class SubjectClient {
  /**
   * Fetch subject details and enrolled students
   */
  static async getSubjectDetails(subjectId: string): Promise<{ data: GetSubjectResponse | null; error: string | null }> {
    try {
      const response = await apiClient.get(`/subject/${subjectId}`);
      return { data: response.data, error: null };
    } catch (error) {
      console.error('Error fetching subject details:', error);
      return { data: null, error: 'Failed to fetch subject details' };
    }
  }

  /**
   * Get all enrolled students for a subject
   */
  static async getSubjectStudents(subjectId: string): Promise<{ data: EnrolledStudent[] | null; error: string | null }> {
    try {
      const response = await apiClient.get(`/subject/${subjectId}/students`);
      return { data: response.data, error: null };
    } catch (error) {
      console.error('Error fetching subject students:', error);
      return { data: null, error: 'Failed to fetch students' };
    }
  }

  /**
   * Get attendance stats for quick display
   */
  static async getSubjectStats(subjectId: string, userId: string): Promise<{ data: any | null; error: string | null }> {
    try {
      const response = await apiClient.get(`/subject/${subjectId}/stats?userId=${userId}`);
      return { data: response.data, error: null };
    } catch (error) {
      console.error('Error fetching subject stats:', error);
      return { data: null, error: 'Failed to fetch subject stats' };
    }
  }
}