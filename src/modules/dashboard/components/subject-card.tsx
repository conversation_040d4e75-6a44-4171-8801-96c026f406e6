'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { EnrolledSubject } from '../types/subject.types'
import { cn } from '@/lib/utils'
import { Calendar, MessageCircle, TrendingUp } from 'lucide-react'

interface SubjectCardProps {
  subject: EnrolledSubject
  onViewDetails: (subjectId: string) => void
  onQuickChat?: (subjectId: string) => void
  onMarkAttendance?: (subjectId: string) => void
  loading?: boolean
}

export function SubjectCard({
  subject,
  onViewDetails,
  onQuickChat,
  onMarkAttendance,
  loading = false,
}: SubjectCardProps) {
  const attendancePercentage = subject.stats.attendance_percentage

  const getAttendanceColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600 bg-green-50'
    if (percentage >= 60) return 'text-yellow-600 bg-yellow-50'
    return 'text-red-600 bg-red-50'
  }

  const getAttendanceStatus = (percentage: number) => {
    if (percentage >= 80) return 'Good'
    if (percentage >= 60) return 'Fair'
    return 'Poor'
  }

  if (loading) {
    return (
      <Card className="animate-pulse">
        <CardHeader>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-2/3"></div>
          </div>
        </CardContent>
        <CardFooter>
          <div className="h-8 bg-gray-200 rounded w-full"></div>
        </CardFooter>
      </Card>
    )
  }

  return (
    <Card className="group hover:shadow-md transition-all duration-200 cursor-pointer border-l-4 border-l-primary/20 hover:border-l-primary flex flex-col h-full">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold group-hover:text-primary transition-colors">
          {subject.name}
        </CardTitle>
        <div className="text-sm text-muted-foreground">
          <span className="font-medium text-primary">{subject.code}</span>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col">
        <div className="space-y-4">
          {/* Attendance Status */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Attendance</span>
            <div className={cn(
              "px-2 py-1 rounded-full text-xs font-medium",
              getAttendanceColor(attendancePercentage)
            )}>
              {attendancePercentage.toFixed(1)}% ({getAttendanceStatus(attendancePercentage)})
            </div>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={cn(
                "h-2 rounded-full transition-all duration-300",
                attendancePercentage >= 80 ? "bg-green-500" :
                attendancePercentage >= 60 ? "bg-yellow-500" : "bg-red-500"
              )}
              style={{ width: `${attendancePercentage}%` }}
            />
          </div>

          {/* Class Stats */}
          <div className="space-y-3 text-sm">
            <div>
              <p className="text-muted-foreground">Classes Attended</p>
              <p className="font-semibold">
                {subject.stats.attended_classes}/{subject.stats.total_classes}
              </p>
            </div>
            <div>
              <p className="text-muted-foreground">Last Attended</p>
              <p className="font-semibold">
                {subject.stats.last_attendance
                  ? new Date(subject.stats.last_attendance).toLocaleDateString()
                  : 'Never'
                }
              </p>
            </div>
          </div>
        </div>
        <div className="flex-1"></div>
      </CardContent>

      <CardFooter className="flex gap-2 pt-4">
        <Button
          onClick={() => onViewDetails(subject.id)}
          className="flex-1"
          size="sm"
        >
          <TrendingUp className="h-4 w-4 mr-1" />
          View Details
        </Button>

        {onQuickChat && (
          <Button
            onClick={() => onQuickChat(subject.id)}
            variant="outline"
            size="sm"
          >
            <MessageCircle className="h-4 w-4" />
          </Button>
        )}

        {onMarkAttendance && (
          <Button
            onClick={() => onMarkAttendance(subject.id)}
            variant="outline"
            size="sm"
          >
            <Calendar className="h-4 w-4" />
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}