import { NextRequest, NextResponse } from 'next/server';
import { apiVerifyOTPSchema } from '@/modules/auth/api/auth-validations';
import {
   verifyAndUseOTP,
   findUserByEmail,
   updateUserEmailVerification,
} from '@/modules/auth/api/auth-database-server';
import { generateAccessToken, generateRefreshToken } from '@/lib/auth';
import { sendWelcomeEmail } from '@/lib/email';
import { AuthSession } from '@/types/auth';

export async function POST(request: NextRequest) {
  try {
     const body = await request.json();

     // Validate request body
     const validationResult = apiVerifyOTPSchema.safeParse(body);
     if (!validationResult.success) {
        return NextResponse.json(
           {
              success: false,
              error: 'Invalid input data',
              details: validationResult.error.issues,
           },
           { status: 400 }
        );
     }

     const { email, otp } = validationResult.data;

     // Verify OTP
     const { valid: otpValid, error: otpError } = await verifyAndUseOTP(email, otp);

     if (!otpValid) {
        return NextResponse.json(
           {
              success: false,
              error: otpError || 'Invalid or expired OTP',
           },
           { status: 400 }
        );
     }

     // Get user details
     const { user, error: userError } = await findUserByEmail(email);

     if (userError || !user) {
        return NextResponse.json(
           {
              success: false,
              error: 'User not found',
           },
           { status: 404 }
        );
     }

     // Update user email verification status
     const { success: verificationUpdated, error: verificationError } =
        await updateUserEmailVerification(email, true);

     if (!verificationUpdated) {
        console.error('Failed to update email verification:', verificationError);
        return NextResponse.json(
           {
              success: false,
              error: 'Failed to verify email',
           },
           { status: 500 }
        );
     }

     // Generate tokens
     const tokenPayload = {
        userId: user.id,
        email: user.email,
        verified: true,
     };

     const accessToken = generateAccessToken(tokenPayload);
     const refreshToken = generateRefreshToken(tokenPayload);

     // Create session
     const session: AuthSession = {
        user: { ...user, emailVerified: true },
        accessToken,
        refreshToken,
        expiresAt: Date.now() + 15 * 60 * 1000, // 15 minutes from now
     };

     // Send welcome email (don't await to avoid blocking response)
     // TODO: Fetch course name from courseId and pass to welcome email
     sendWelcomeEmail(user.email, user.firstName).catch(error => {
        console.error('Failed to send welcome email:', error);
     });

     // Set HTTP-only cookies for tokens
     const response = NextResponse.json(
        {
           success: true,
           message: 'Email verified successfully',
           user: session.user,
           session,
        },
        { status: 200 }
     );

     // Set secure cookies
     response.cookies.set('accessToken', accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 15 * 60, // 15 minutes
        path: '/',
     });

     response.cookies.set('refreshToken', refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 7 * 24 * 60 * 60, // 7 days
        path: '/',
     });

     return response;
  } catch (error) {
    console.error('OTP verification error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}