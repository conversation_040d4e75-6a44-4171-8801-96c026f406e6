/**
 * User interface
 */
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  courseId: number;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Auth session interface
 */
export interface AuthSession {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

/**
 * Auth state interface for Zustand store
 */
export interface AuthState {
  user: User | null;
  session: AuthSession | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

/**
 * Auth actions interface for Zustand store
 */
export interface AuthActions {
  login: (email: string, password: string) => Promise<AuthResponse>;
  signup: (data: SignupData) => Promise<AuthResponse>;
  verifyOTP: (email: string, otp: string) => Promise<AuthResponse>;
  resendOTP: (email: string) => Promise<AuthResponse>;
  logout: () => Promise<void>;
  forgotPassword: (email: string) => Promise<AuthResponse>;
  resetPassword: (token: string, password: string) => Promise<AuthResponse>;
  refreshToken: () => Promise<boolean>;
  initializeAuth: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
}

/**
 * Complete auth store interface
 */
export interface AuthStore extends AuthState, AuthActions {}

/**
 * Signup data interface
 */
export interface SignupData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  courseId: number;
}

/**
 * Login data interface
 */
export interface LoginData {
  email: string;
  password: string;
}

/**
 * OTP verification data interface
 */
export interface OTPVerificationData {
  email: string;
  otp: string;
}

/**
 * Auth response interface
 */
export interface AuthResponse {
  success: boolean;
  message?: string;
  error?: string;
  user?: User;
  session?: AuthSession;
  requiresVerification?: boolean;
}

/**
 * OTP record interface (for database)
 */
export interface OTPRecord {
  id: string;
  email: string;
  otp: string;
  expiresAt: string;
  used: boolean;
  createdAt: string;
}

/**
 * Password reset token interface (for database)
 */
export interface PasswordResetToken {
  id: string;
  email: string;
  token: string;
  expiresAt: string;
  used: boolean;
  createdAt: string;
}

/**
 * Course interface
 */
export interface Course {
  id: number;
  name: string;
  code: string;
  department: string;
}

/**
 * Auth context interface (for legacy support if needed)
 */
export interface AuthContextType extends AuthState, AuthActions {}

/**
 * API error interface
 */
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

/**
 * Protected route props interface
 */
export interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredAuth?: boolean;
  redirectTo?: string;
  loadingComponent?: React.ComponentType;
}

/**
 * Auth form states interface
 */
export interface AuthFormState {
  isLoading: boolean;
  error: string | null;
  success: string | null;
}

/**
 * Token payload interface (extends JWT payload from lib/auth.ts)
 */
export interface TokenPayload {
  userId: string;
  email: string;
  verified: boolean;
  iat?: number;
  exp?: number;
}

/**
 * Middleware auth result interface
 */
export interface MiddlewareAuthResult {
  authenticated: boolean;
  user?: User;
  error?: string;
}

/**
 * Auth hook return type
 */
export interface UseAuthReturn extends AuthState, AuthActions {
  isInitialized: boolean;
}

/**
 * Email verification status
 */
export enum EmailVerificationStatus {
  PENDING = 'pending',
  VERIFIED = 'verified',
  EXPIRED = 'expired',
  FAILED = 'failed',
}

/**
 * Auth action types for potential logging/analytics
 */
export enum AuthActionType {
  LOGIN_ATTEMPT = 'login_attempt',
  LOGIN_SUCCESS = 'login_success',
  LOGIN_FAILURE = 'login_failure',
  SIGNUP_ATTEMPT = 'signup_attempt',
  SIGNUP_SUCCESS = 'signup_success',
  SIGNUP_FAILURE = 'signup_failure',
  OTP_SENT = 'otp_sent',
  OTP_VERIFIED = 'otp_verified',
  OTP_FAILED = 'otp_failed',
  LOGOUT = 'logout',
  TOKEN_REFRESHED = 'token_refreshed',
  PASSWORD_RESET_REQUESTED = 'password_reset_requested',
  PASSWORD_RESET_COMPLETED = 'password_reset_completed',
}

/**
 * Database user interface (matches Supabase schema)
 */
export interface DatabaseUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  course_id: number;
  password_hash: string;
  email_verified: boolean;
  created_at: string;
  updated_at: string;
}