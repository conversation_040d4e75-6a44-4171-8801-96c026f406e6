import { EnrolledSubject, SubjectStats } from '../types/subject.types'

/**
 * Format attendance percentage with proper decimal places
 */
export function formatAttendancePercentage(percentage: number): string {
  return `${percentage.toFixed(1)}%`
}

/**
 * Get attendance status based on percentage
 */
export function getAttendanceStatus(percentage: number): 'good' | 'warning' | 'poor' {
  if (percentage >= 80) return 'good'
  if (percentage >= 60) return 'warning'
  return 'poor'
}

/**
 * Get attendance status text
 */
export function getAttendanceStatusText(percentage: number): string {
  const status = getAttendanceStatus(percentage)
  switch (status) {
    case 'good':
      return 'Good'
    case 'warning':
      return 'Fair'
    case 'poor':
      return 'Poor'
  }
}

/**
 * Get attendance status color classes
 */
export function getAttendanceStatusColor(percentage: number): string {
  const status = getAttendanceStatus(percentage)
  switch (status) {
    case 'good':
      return 'text-green-600 bg-green-50'
    case 'warning':
      return 'text-yellow-600 bg-yellow-50'
    case 'poor':
      return 'text-red-600 bg-red-50'
  }
}

/**
 * Sort subjects by attendance percentage (descending)
 */
export function sortSubjectsByAttendance(subjects: EnrolledSubject[]): EnrolledSubject[] {
  return [...subjects].sort((a, b) =>
    b.stats.attendance_percentage - a.stats.attendance_percentage
  )
}

/**
 * Sort subjects by name (ascending)
 */
export function sortSubjectsByName(subjects: EnrolledSubject[]): EnrolledSubject[] {
  return [...subjects].sort((a, b) => a.name.localeCompare(b.name))
}

/**
 * Filter subjects by attendance status
 */
export function filterSubjectsByStatus(
  subjects: EnrolledSubject[],
  status: 'good' | 'warning' | 'poor'
): EnrolledSubject[] {
  return subjects.filter(subject =>
    getAttendanceStatus(subject.stats.attendance_percentage) === status
  )
}

/**
 * Calculate overall statistics from subjects array
 */
export function calculateOverallStats(subjects: EnrolledSubject[]) {
  if (subjects.length === 0) {
    return {
      total_subjects: 0,
      avg_attendance: 0,
      total_classes_attended: 0,
    }
  }

  const totalClassesAttended = subjects.reduce(
    (sum, subject) => sum + subject.stats.attended_classes,
    0
  )

  const avgAttendance = subjects.reduce(
    (sum, subject) => sum + subject.stats.attendance_percentage,
    0
  ) / subjects.length

  return {
    total_subjects: subjects.length,
    avg_attendance: Math.round(avgAttendance * 10) / 10,
    total_classes_attended: totalClassesAttended,
  }
}

/**
 * Format date string to readable format
 */
export function formatDate(dateString: string | null): string {
  if (!dateString) return 'Never'

  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  } catch {
    return 'Invalid date'
  }
}

/**
 * Format relative time (e.g., "2 days ago")
 */
export function formatRelativeTime(dateString: string | null): string {
  if (!dateString) return 'Never'

  try {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMs = now.getTime() - date.getTime()
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))

    if (diffInDays === 0) return 'Today'
    if (diffInDays === 1) return 'Yesterday'
    if (diffInDays < 7) return `${diffInDays} days ago`
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`
    return `${Math.floor(diffInDays / 365)} years ago`
  } catch {
    return 'Invalid date'
  }
}

/**
 * Check if subject needs attention (low attendance)
 */
export function subjectNeedsAttention(subject: EnrolledSubject): boolean {
  return subject.stats.attendance_percentage < 60
}

/**
 * Get subject progress bar color
 */
export function getProgressBarColor(percentage: number): string {
  if (percentage >= 80) return 'bg-green-500'
  if (percentage >= 60) return 'bg-yellow-500'
  return 'bg-red-500'
}