'use client';

import { create } from 'zustand';
import { SubjectDetails, EnrolledStudent } from '../types';
import { SubjectClient, ConversationClient } from '../api';

interface SubjectState {
  currentSubject: SubjectDetails | null;
  students: EnrolledStudent[];
  loading: boolean;
  error: string | null;
}

interface SubjectActions {
  fetchSubjectData: (subjectId: string) => Promise<void>;
  createDirectChat: (participantId: string) => Promise<string>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

type SubjectStore = SubjectState & SubjectActions;

export const useSubjectStore = create<SubjectStore>((set, get) => ({
  // Initial state
  currentSubject: null,
  students: [],
  loading: false,
  error: null,

  // Actions
  fetchSubjectData: async (subjectId: string) => {
    set({ loading: true, error: null });

    try {
      const { data, error } = await SubjectClient.getSubjectDetails(subjectId);

      if (error) {
        set({ loading: false, error });
      } else if (data) {
        set({
          loading: false,
          currentSubject: data.subject,
          students: data.students,
          error: null
        });
      }
    } catch (error) {
      console.error('Error fetching subject data:', error);
      set({ loading: false, error: 'Failed to load subject data' });
    }
  },

  createDirectChat: async (participantId: string) => {
    try {
      const { data, error } = await ConversationClient.getOrCreateDirectChat(participantId);

      if (error || !data) {
        throw new Error(error || 'Failed to create chat');
      }

      return data.conversation_id;
    } catch (error) {
      console.error('Error creating direct chat:', error);
      set({ error: 'Failed to create chat' });
      throw error;
    }
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },
}));

// Individual selectors for reactive state
export const useCurrentSubject = () => useSubjectStore(state => state.currentSubject);
export const useSubjectStudents = () => useSubjectStore(state => state.students);
export const useSubjectLoading = () => useSubjectStore(state => state.loading);
export const useSubjectError = () => useSubjectStore(state => state.error);