'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useDashboardStats } from '../hooks/use-dashboard-stats'
import { BookOpen, TrendingUp, Calendar, BarChart3 } from 'lucide-react'
import { cn } from '@/lib/utils'

export function DashboardStats() {
  const { stats, loading } = useDashboardStats()

  if (loading || !stats) {
    return <DashboardStatsLoading />
  }

  const statCards = [
    {
      title: 'Total Subjects',
      value: stats.total_subjects,
      icon: BookOpen,
      color: 'text-blue-600 bg-blue-50',
    },
    {
      title: 'Overall Attendance',
      value: `${stats.avg_attendance}%`,
      icon: TrendingUp,
      color: stats.avg_attendance >= 80 ? 'text-green-600 bg-green-50' :
             stats.avg_attendance >= 60 ? 'text-yellow-600 bg-yellow-50' :
             'text-red-600 bg-red-50',
    },
    {
      title: 'Classes Attended',
      value: stats.total_classes_attended,
      icon: Calendar,
      color: 'text-purple-600 bg-purple-50',
    },
    {
      title: 'Attendance Status',
      value: stats.avg_attendance >= 80 ? 'Good' :
             stats.avg_attendance >= 60 ? 'Fair' : 'Poor',
      icon: BarChart3,
      color: stats.avg_attendance >= 80 ? 'text-green-600 bg-green-50' :
             stats.avg_attendance >= 60 ? 'text-yellow-600 bg-yellow-50' :
             'text-red-600 bg-red-50',
    },
  ]

  return (
    <div className="grid gap-4 md:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
      {statCards.map((stat, index) => (
        <Card key={index} className="border-l-4 border-l-primary/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {stat.title}
            </CardTitle>
            <div className={cn("p-2 rounded-full", stat.color)}>
              <stat.icon className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

function DashboardStatsLoading() {
  return (
    <div className="grid gap-4 md:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
      {Array.from({ length: 4 }, (_, i) => (
        <Card key={i} className="animate-pulse">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="h-4 bg-gray-200 rounded w-24" />
            <div className="h-8 w-8 bg-gray-200 rounded-full" />
          </CardHeader>
          <CardContent>
            <div className="h-8 bg-gray-200 rounded w-16" />
          </CardContent>
        </Card>
      ))}
    </div>
  )
}