# Module 3: Subject Detail & Student Management - Complete Implementation Plan

## Overview
Module 3 will implement the subject detail page where students can view all enrolled classmates, start individual chats, join group chats, and access attendance marking functionality.

---

## Phase 1: Data Layer & API Foundation

### 1.1 Create Subject Module Structure
Create the standard modular structure following our folder architecture:

```
src/modules/subject/
├── components/       # All React components specific to subject module
├── pages/           # Page components (imported by app/ routes)
├── api/             # API client functions and utilities
├── hooks/           # Custom React hooks
├── schemas/         # Zod schemas, validation schemas
├── types/           # TypeScript type definitions
├── constants/       # Module-specific constants
├── utils/           # Utility functions
├── store/           # State management (Zustand)
└── index.ts         # Barrel exports
```

### 1.2 Database Operations Library

**File: `src/modules/subject/api/subject-client.ts`**
- `getSubjectDetails(subjectId)` - Fetch subject information
- `getSubjectStudents(subjectId)` - Get all enrolled students
- `getSubjectStats(subjectId, userId)` - Get attendance stats for quick display

**File: `src/modules/subject/api/conversation-client.ts`**
- `getOrCreateDirectChat(userId1, userId2)` - Create or retrieve direct conversation
- `getGroupChatBySubject(subjectId)` - Get group chat for subject
- `createConversation(type, participants, subjectId?)` - Generic conversation creator

### 1.3 API Routes Setup

**Protected Routes (using `withAuth` pattern):**
- `app/api/subject/[id]/route.ts` - GET subject details
- `app/api/subject/[id]/students/route.ts` - GET enrolled students
- `app/api/conversations/create-direct/route.ts` - POST create direct chat
- `app/api/conversations/group/[subjectId]/route.ts` - GET/POST group chat

---

## Phase 2: State Management & Types

### 2.1 TypeScript Types

**File: `src/modules/subject/types/subject.types.ts`**
```typescript
export interface SubjectDetails {
  id: string;
  name: string;
  code: string;
  description?: string;
  course_id: string;
  created_at: string;
}

export interface EnrolledStudent {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  course: string;
  enrollment_date: string;
  avatar_url?: string;
}

export interface ConversationData {
  id: string;
  type: 'direct' | 'group';
  participants: string[];
  subject_id?: string;
  created_at: string;
}
```

**File: `src/modules/subject/types/api.types.ts`**
```typescript
export interface GetSubjectResponse {
  subject: SubjectDetails;
  students: EnrolledStudent[];
}

export interface CreateChatRequest {
  participant_id: string;
}

export interface CreateChatResponse {
  conversation_id: string;
  redirect_url: string;
}
```

### 2.2 Zustand Store (Following State Management Guide)

**File: `src/modules/subject/store/subject-store.ts`**

Following the established patterns from the Zustand guide:
- Individual selectors for reactive state
- Direct `getState()` access for actions
- Main hook combining reactive state + actions

```typescript
interface SubjectStore {
  // State
  currentSubject: SubjectDetails | null;
  students: EnrolledStudent[];
  loading: boolean;
  error: string | null;

  // Actions
  fetchSubjectData: (subjectId: string) => Promise<void>;
  createDirectChat: (participantId: string) => Promise<string>;
  clearError: () => void;
}

// Individual selectors for reactive state
export const useCurrentSubject = () => useSubjectStore(state => state.currentSubject);
export const useSubjectStudents = () => useSubjectStore(state => state.students);
export const useSubjectLoading = () => useSubjectStore(state => state.loading);
export const useSubjectError = () => useSubjectStore(state => state.error);

// Main hook with direct action access
export function useSubject(subjectId: string) {
  // Reactive state for UI updates
  const currentSubject = useCurrentSubject();
  const students = useSubjectStudents();
  const loading = useSubjectLoading();
  const error = useSubjectError();

  // Direct state access for actions (no re-renders)
  const { fetchSubjectData, createDirectChat, clearError } = useSubjectStore.getState();

  useEffect(() => {
    if (subjectId && (!currentSubject || currentSubject.id !== subjectId)) {
      fetchSubjectData(subjectId);
    }
  }, [subjectId, currentSubject, fetchSubjectData]);

  return {
    subject: currentSubject,
    students,
    loading,
    error,
    createChat: createDirectChat,
    clearError,
  };
}
```

### 2.3 Validation Schemas

**File: `src/modules/subject/schemas/subject-schemas.ts`**
```typescript
import { z } from 'zod';

export const createChatSchema = z.object({
  participant_id: z.string().uuid(),
});

export const subjectParamsSchema = z.object({
  id: z.string().uuid(),
});
```

---

## Phase 3: UI Components

### 3.1 Core Subject Components

**File: `src/modules/subject/components/subject-header.tsx`**
- Subject name, code, description display
- Back navigation to dashboard
- Breadcrumb integration

**File: `src/modules/subject/components/subject-actions.tsx`**
- Group chat button
- Attendance button
- Quick action toolbar

**File: `src/modules/subject/components/attendance-quick-action.tsx`**
- One-click attendance marking for today
- Success/error feedback
- Integration with existing attendance system

### 3.2 Student Management Components

**File: `src/modules/subject/components/student-card.tsx`**
```typescript
interface StudentCardProps {
  student: EnrolledStudent;
  onStartChat: (studentId: string) => void;
  isCreatingChat?: boolean;
}
```

Features:
- Student name, course display
- Avatar placeholder or image
- "Start Chat" button with loading state
- Hover effects and accessibility

**File: `src/modules/subject/components/student-grid.tsx`**
- Responsive grid layout (1 col mobile, 2-3 tablet, 4+ desktop)
- Loading skeleton states
- Empty state handling

**File: `src/modules/subject/components/student-list-skeleton.tsx`**
- Loading state using shadcn skeleton components
- Matches actual grid layout

### 3.3 Chat Integration Components

**File: `src/modules/subject/components/chat-button.tsx`**
- Reusable button for starting chats
- Loading states and error handling
- Consistent styling

**File: `src/modules/subject/components/group-chat-button.tsx`**
- Access group chat functionality
- Subject-specific group chat creation

---

## Phase 4: Pages & Navigation

### 4.1 Main Subject Detail Page

**File: `src/modules/subject/pages/subject-detail-page.tsx`**

Layout structure:
```tsx
<div className="container mx-auto p-4">
  <SubjectHeader subject={subject} />
  <SubjectActions subjectId={subjectId} />

  <div className="mt-6">
    <h2>Enrolled Students</h2>
    {loading ? (
      <StudentListSkeleton />
    ) : (
      <StudentGrid
        students={students}
        onStartChat={handleStartChat}
      />
    )}
  </div>
</div>
```

Features:
- Responsive layout
- Loading states with skeletons
- Error handling with retry
- Empty state when no students

### 4.2 App Router Integration

**File: `app/dashboard/subject/[id]/page.tsx`**
```tsx
import { SubjectDetailPage } from '@/modules/subject';

interface PageProps {
  params: { id: string };
}

export default function Page({ params }: PageProps) {
  return <SubjectDetailPage subjectId={params.id} />;
}
```

### 4.3 Navigation Enhancement

- Update dashboard subject cards with proper links
- Add breadcrumb navigation component
- Ensure back navigation preserves dashboard state

---

## Phase 5: Chat Integration & Quick Actions

### 5.1 Chat Navigation Logic

**Chat Creation Flow:**
1. User clicks "Start Chat" on student card
2. Show loading state on button
3. Call `createDirectChat(studentId)` API
4. Redirect to `/chat/[conversationId]` on success
5. Handle errors with toast notifications

**Group Chat Flow:**
1. User clicks "Group Chat" button
2. Check if group chat exists for subject
3. Create if doesn't exist, or redirect to existing
4. Navigate to group chat page

### 5.2 Attendance Quick Action

**One-click Attendance:**
- Check if already marked for today
- Show appropriate button state
- Mark attendance with single click
- Success feedback with toast
- Update dashboard stats if needed

---

## Phase 6: Polish & Responsive Design

### 6.1 Responsive Implementation

**Mobile (< 640px):**
- Single column student grid
- Stack action buttons vertically
- Full-width components

**Tablet (640px - 1024px):**
- 2-3 column student grid
- Horizontal action buttons
- Optimized spacing

**Desktop (> 1024px):**
- 4+ column student grid
- Compact action toolbar
- Maximum readability

### 6.2 Loading & Error States

**Loading States:**
- Skeleton components for all lists
- Individual button loading states
- Page-level loading indicators

**Error Handling:**
- Error boundaries for component failures
- Retry mechanisms for failed operations
- User-friendly error messages

### 6.3 Accessibility & UX

**Accessibility:**
- Proper ARIA labels
- Keyboard navigation support
- Screen reader compatibility
- Focus management

**UX Enhancements:**
- Hover effects on interactive elements
- Loading indicators for all async operations
- Success feedback for completed actions
- Intuitive navigation patterns

---

## Phase 7: Testing & Integration

### 7.1 Component Testing

**Test Coverage:**
- Subject store state management
- Component rendering with different props
- User interaction handling
- Error state handling

### 7.2 API Integration Testing

**API Tests:**
- All endpoints with authentication
- Error handling and edge cases
- Data validation
- Performance with larger datasets

### 7.3 End-to-End Flow Testing

**User Journeys:**
1. Dashboard → Subject Detail → View Students
2. Subject Detail → Start Chat → Chat Page
3. Subject Detail → Group Chat → Group Chat Page
4. Subject Detail → Mark Attendance → Success
5. Mobile and desktop experiences

---

## Implementation Order

### Week 1: Foundation
1. **Day 1-2**: Module structure, types, and API clients
2. **Day 3-4**: Zustand store and validation schemas
3. **Day 5**: API routes and basic testing

### Week 2: UI Development
1. **Day 1-2**: Core subject components
2. **Day 3-4**: Student management components
3. **Day 5**: Main page integration

### Week 3: Integration & Polish
1. **Day 1-2**: Chat integration and navigation
2. **Day 3-4**: Responsive design and accessibility
3. **Day 5**: Testing and bug fixes

---

## Key Implementation Notes

### Architecture Compliance
- **Modular structure**: All code in `src/modules/subject/`
- **Clean imports**: Use barrel exports from module
- **Separation of concerns**: Clear boundaries between layers

### State Management
- **Follow Zustand guide**: Individual selectors + `getState()` pattern
- **Avoid re-renders**: Use direct action access
- **Reactive state**: Only for UI updates

### API Patterns
- **Authentication**: All protected routes use `withAuth`
- **Error handling**: Consistent error responses
- **Validation**: Server-side validation with Zod

### UI Consistency
- **shadcn/ui**: Use existing component library
- **Responsive**: Mobile-first approach
- **Accessibility**: WCAG compliance

---

## Success Criteria

### Functional Requirements
✅ Students can view all classmates in a subject
✅ One-click chat creation with any student
✅ Easy access to group chat for subject
✅ Quick attendance marking functionality
✅ Proper error handling and loading states

### Technical Requirements
✅ Follows modular architecture
✅ Uses proper Zustand patterns
✅ Implements auth protection
✅ Responsive design works on all devices
✅ Accessible to all users

### Performance Requirements
✅ Fast loading with skeleton states
✅ Optimized re-rendering
✅ Efficient API calls
✅ Smooth navigation experience

---

## Dependencies & Integration Points

### Existing Modules
- **Auth Module**: User authentication and session management
- **Dashboard Module**: Navigation and subject listing
- **Future Chat Module**: Chat page integration

### External Services
- **Supabase**: Database operations and real-time features
- **Next.js**: App routing and API routes
- **shadcn/ui**: Component library

### Configuration
- **Environment**: Development and production settings
- **Database**: Subject and conversation tables
- **Routing**: App router configuration

---

This plan provides a comprehensive roadmap for implementing Module 3 while maintaining consistency with existing architecture and preparing for future modules.