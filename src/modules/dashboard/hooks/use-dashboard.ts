'use client'

import { useEffect } from 'react'
import { useDashboardStore } from '../store/dashboard-store'

/**
 * Main hook for dashboard data management
 * Handles data loading, error states, and provides refresh functionality
 */
export function useDashboard() {
   const { data, loading, error, fetchDashboardData, refreshSubjects, clearError } = useDashboardStore();

   // Load dashboard data on mount
   useEffect(() => {
      if (!data && !loading) {
         fetchDashboardData();
      }
   }, [data, loading, fetchDashboardData]);

   return {
      data,
      loading,
      error,
      refresh: refreshSubjects,
      clearError,
   };
}

