'use client';

import { useMemo } from 'react';
import { useDashboardStore } from '../store/dashboard-store';
import { EnrolledSubject } from '../types/subject.types';

/**
 * Hook for subject-specific operations and filtering
 */
export function useSubjects() {
   const { data, loading, error } = useDashboardStore();
   const subjects = data?.subjects || [];

   // Memoized computed values
   const sortedSubjects = useMemo(() => {
      return [...subjects].sort((a, b) => a.name.localeCompare(b.name));
   }, [subjects]);

   const subjectsByAttendance = useMemo(() => {
      return [...subjects].sort(
         (a, b) => b.stats.attendance_percentage - a.stats.attendance_percentage
      );
   }, [subjects]);

   const getSubjectById = useMemo(() => {
      return (id: string): EnrolledSubject | undefined => {
         return subjects.find(subject => subject.id === id);
      };
   }, [subjects]);

   // Filter subjects by attendance status
   const subjectsByStatus = useMemo(() => {
      const good = subjects.filter(s => s.stats.attendance_percentage >= 80);
      const warning = subjects.filter(
         s => s.stats.attendance_percentage >= 60 && s.stats.attendance_percentage < 80
      );
      const poor = subjects.filter(s => s.stats.attendance_percentage < 60);

      return { good, warning, poor };
   }, [subjects]);

   return {
      subjects,
      sortedSubjects,
      subjectsByAttendance,
      subjectsByStatus,
      getSubjectById,
      loading,
      error,
      isEmpty: subjects.length === 0,
   };
}
