'use client'

import { EnrolledSubject } from '../types/subject.types'
import { SubjectCard } from './subject-card'
import { LoadingGrid } from './loading-grid'
import { EmptyState } from './empty-state'
import { cn } from '@/lib/utils'
import { useRouter } from 'next/navigation'

interface SubjectGridProps {
  subjects: EnrolledSubject[] | undefined
  loading: boolean
  error: string | null
  className?: string
}

export function SubjectGrid({ subjects, loading, error, className }: SubjectGridProps) {
  const router = useRouter()

  const handleViewDetails = (subjectId: string) => {
    router.push(`/dashboard/subject/${subjectId}`)
  }

  const handleQuickChat = (subjectId: string) => {
    router.push(`/dashboard/subjects/${subjectId}/chat`)
  }

  const handleMarkAttendance = (subjectId: string) => {
    router.push(`/dashboard/subjects/${subjectId}/attendance`)
  }

  if (loading) {
    return <LoadingGrid />
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-red-600 font-medium">Error loading subjects</p>
          <p className="text-gray-600 text-sm mt-1">{error}</p>
        </div>
      </div>
    )
  }

  if (!subjects || subjects.length === 0) {
    return <EmptyState />
  }

  return (
    <div className={cn(
      "grid gap-6",
      "grid-cols-1",
      "sm:grid-cols-2",
      "lg:grid-cols-3",
      "xl:grid-cols-4",
      className
    )}>
      {subjects.map((subject) => (
        <SubjectCard
          key={subject.id}
          subject={subject}
          onViewDetails={handleViewDetails}
          onQuickChat={handleQuickChat}
          onMarkAttendance={handleMarkAttendance}
        />
      ))}
    </div>
  )
}