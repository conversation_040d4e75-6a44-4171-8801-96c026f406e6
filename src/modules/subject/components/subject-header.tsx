'use client';

import { But<PERSON> } from '@/components/ui/button';
import { SubjectDetails } from '../types';
import { ArrowLeft, Book } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface SubjectHeaderProps {
  subject: SubjectDetails;
}

export function SubjectHeader({ subject }: SubjectHeaderProps) {
  const router = useRouter();

  const handleBackClick = () => {
    router.push('/dashboard/home');
  };

  return (
    <header className="bg-white/90 backdrop-blur-md border-b border-gray-200/50 shadow-lg">
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          {/* Back Button and Subject Info */}
          <div className="flex items-center gap-6">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBackClick}
              className="flex items-center gap-2 hover:bg-primary hover:text-white transition-all duration-200 border-gray-300 hover:border-primary"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden sm:inline">Back to Dashboard</span>
            </Button>

            <div className="flex items-center gap-4">
              <div className="p-3 bg-primary/10 rounded-xl">
                <Book className="h-8 w-8 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 mb-1">
                  {subject.name}
                </h1>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span className="font-medium bg-gray-100 px-2 py-1 rounded-md">
                    {subject.code}
                  </span>
                  {subject.description && (
                    <>
                      <span className="text-gray-400">•</span>
                      <span>{subject.description}</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Breadcrumb */}
          <div className="hidden md:flex items-center text-sm">
            <span className="text-gray-500">Dashboard</span>
            <span className="mx-2 text-gray-400">/</span>
            <span className="text-primary font-semibold">{subject.name}</span>
          </div>
        </div>
      </div>
    </header>
  );
}