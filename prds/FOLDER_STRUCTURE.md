# Project Folder Structure

## Core Philosophy

This project follows a **modular architecture** where each feature/domain is self-contained within its own module. This approach promotes:

- **High Cohesion**: Related functionality stays together
- **Low Coupling**: Modules are independent and loosely coupled
- **Easy Maintenance**: Each module contains everything it needs
- **Clear Boundaries**: Domain logic is clearly separated

## Structure Overview

```
src/
├── modules/           # Self-contained feature modules
│   ├── auth/         # Authentication module
│   ├── dashboard/    # Dashboard module
│   ├── courses/      # Courses module (example)
│   └── ...           # Other feature modules
└── app/              # Next.js App Router (routing only)
    ├── auth/         # Auth routes (imports from modules/auth)
    ├── dashboard/    # Dashboard routes (imports from modules/dashboard)
    ├── api/          # API routes
    └── ...           # Other routes
```

## Module Structure

Each module follows this standardized structure:

```
src/modules/[module-name]/
├── components/       # All React components specific to this module
├── pages/           # Page components (imported by app/ routes)
├── api/             # API client functions and utilities
├── hooks/           # Custom React hooks
├── schemas/         # Zod schemas, validation schemas
├── types/           # TypeScript type definitions
├── constants/       # Module-specific constants
├── utils/           # Utility functions
├── store/           # State management (Zustand, Redux, etc.)
```

## App Router Structure

The `src/app/` directory is used **exclusively for routing** and should contain minimal logic:

```
src/app/
├── layout.tsx              # Root layout
├── page.tsx                # Home page
├── globals.css             # Global styles
├── [feature]/              # Feature routes
│   ├── page.tsx           # Route page (imports from modules/[feature])
│   ├── layout.tsx         # Feature layout (optional)
│   └── [sub-route]/       # Sub-routes
└── api/                    # API routes
    └── [feature]/          # Feature API routes
        └── route.ts        # API handlers
```

## Example: Auth Module

```
src/modules/auth/
├── components/
│   ├── password-input.tsx
│   ├── auth-card.tsx
│   ├── login-form.tsx
│   ├── signup-form.tsx
│   ├── forgot-password-form.tsx
│   └── auth-provider.tsx
├── pages/
│   ├── login-page.tsx
│   ├── signup-page.tsx
│   ├── forgot-password-page.tsx
│   └── reset-password-page.tsx
├── api/
│   ├── auth-client.ts
│   └── endpoints.ts
├── hooks/
│   ├── use-auth.ts
│   └── use-login.ts
├── schemas/
│   ├── auth-schemas.ts
│   └── validation.ts
├── types/
│   ├── auth.types.ts
│   └── api.types.ts
├── store/
│   └── auth-store.ts
├── constants/
│   └── auth-constants.ts
└── utils/
    ├── auth-utils.ts
    └── token-utils.ts
```

## Corresponding App Routes

```
src/app/auth/
├── login/
│   └── page.tsx          # import { LoginPage } from '@/modules/auth'
├── signup/
│   └── page.tsx          # import { SignupPage } from '@/modules/auth'
├── forgot-password/
│   └── page.tsx          # import { ForgotPasswordPage } from '@/modules/auth'
└── reset-password/
    └── page.tsx          # import { ResetPasswordPage } from '@/modules/auth'
```

## Key Principles

### 1. Self-Contained Modules
- Each module contains everything it needs to function
- Minimal dependencies between modules
- Clear public API through barrel exports

### 2. App Router as Pure Router
- App routes only handle routing and import from modules
- No business logic in app routes
- Simple, clean route definitions

### 3. Consistent Module Structure
- Every module follows the same folder structure
- Predictable file locations
- Easy to navigate and understand

### 4. Clear Separation of Concerns
- **Pages**: Top-level page components
- **Components**: Reusable UI components
- **API**: External communication
- **Hooks**: Reusable logic
- **Store**: State management
- **Types**: Type definitions
- **Schemas**: Data validation
- **Utils**: Helper functions

## Import Patterns

### From App Routes
```typescript
// app/auth/login/page.tsx
import { LoginPage } from '@/modules/auth/pages/login-page'

export default function Page() {
  return <LoginPage />
}
```

### Within Modules
```typescript
// modules/auth/components/login-form.tsx
import { useAuth } from '../hooks/use-auth'
import { loginSchema } from '../schemas/auth-schemas'
import { AuthCard } from './auth-card'
```

### Cross-Module Imports (Discouraged)
```typescript
// If absolutely necessary, import from module's public API
import { UserType } from '@/modules/user/types'
```

## Benefits

- **Scalability**: Easy to add new features as modules
- **Maintainability**: Changes are localized to specific modules
- **Team Collaboration**: Teams can work on different modules independently
- **Code Reusability**: Modules can be easily extracted or reused
- **Testing**: Each module can be tested in isolation
- **Developer Experience**: Predictable structure improves productivity