'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { EnrolledStudent } from '../types';
import { MessageCircle, User } from 'lucide-react';

interface StudentCardProps {
  student: EnrolledStudent;
  onStartChat: (studentId: string) => void;
  isCreatingChat?: boolean;
  isCurrentUser?: boolean;
}

export function StudentCard({
  student,
  onStartChat,
  isCreatingChat = false,
  isCurrentUser = false
}: StudentCardProps) {
  const handleStartChat = () => {
    if (!isCreatingChat) {
      onStartChat(student.id);
    }
  };

  const getInitials = () => {
    const first = student.first_name?.[0] || '';
    const last = student.last_name?.[0] || '';
    return (first + last).toUpperCase();
  };

  return (
    <Card className="group hover:shadow-xl hover:shadow-primary/5 transition-all duration-300 border border-gray-200/50 hover:border-primary/40 bg-white/80 backdrop-blur-sm hover:-translate-y-1">
      <CardContent className="p-6">
        <div className="flex flex-col items-center text-center space-y-4">
          {/* Avatar */}
          <div className="relative">
            {student.avatar_url ? (
              <Avatar className="w-20 h-20 ring-4 ring-white shadow-lg">
                <img
                  src={student.avatar_url}
                  alt={`${student.first_name} ${student.last_name}`}
                  className="w-full h-full object-cover"
                />
              </Avatar>
            ) : (
              <div className="w-20 h-20 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full flex items-center justify-center ring-4 ring-white shadow-lg">
                <span className="text-xl font-bold text-primary">
                  {getInitials() || <User className="h-8 w-8" />}
                </span>
              </div>
            )}
            {isCurrentUser && (
              <Badge
                variant="default"
                className="absolute -top-1 -right-1 text-xs px-2 py-1 bg-gradient-to-r from-primary to-primary/80 text-white shadow-md border-2 border-white"
              >
                You
              </Badge>
            )}
          </div>

          {/* Student Info */}
          <div className="space-y-2 w-full">
            <h3 className="font-bold text-lg text-gray-900 group-hover:text-primary transition-colors leading-tight">
              {student.first_name} {student.last_name}
            </h3>
            <p className="text-sm text-gray-600 font-medium">{student.email}</p>
            <div className="inline-flex items-center px-3 py-1 rounded-full bg-gray-100 text-xs font-medium text-gray-700">
              {student.course}
            </div>
          </div>

          {/* Enrollment Date */}
          <div className="text-xs text-gray-500 bg-gray-50 px-3 py-1 rounded-full">
            Enrolled: {new Date(student.enrollment_date).toLocaleDateString()}
          </div>

          {/* Start Chat Button */}
          {!isCurrentUser ? (
            <Button
              onClick={handleStartChat}
              disabled={isCreatingChat}
              size="sm"
              className="w-full flex items-center gap-2 bg-white text-primary border-2 border-primary/20 hover:bg-primary hover:text-white hover:border-primary transition-all duration-200 font-medium shadow-sm"
              variant="outline"
            >
              <MessageCircle className="h-4 w-4" />
              {isCreatingChat ? 'Creating...' : 'Start Chat'}
            </Button>
          ) : (
            <div className="w-full h-9 flex items-center justify-center text-sm text-gray-500 bg-gray-50 rounded-md font-medium">
              Current Student
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}