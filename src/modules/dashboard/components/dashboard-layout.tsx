'use client'

import { ReactNode } from 'react'
import { cn } from '@/lib/utils'
import { DashboardHeader } from './dashboard-header'

interface DashboardLayoutProps {
  children: ReactNode
  className?: string
}

export function DashboardLayout({ children, className }: DashboardLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      <DashboardHeader />

      <main className={cn(
        "container mx-auto px-4 py-6 space-y-6",
        className
      )}>
        {children}
      </main>
    </div>
  )
}