import { createClient } from '@/lib/supabase/server';
import { SubjectDetails, EnrolledStudent, GetSubjectResponse, SubjectStudentsQueryResult } from '../types';

/**
 * Server-side API functions for subject operations
 */

/**
 * Get subject details and enrolled students
 */
export async function getSubjectDetails(subjectId: string, userId: string): Promise<{ data: GetSubjectResponse | null; error: string | null }> {
  try {
    const supabase = await createClient();

    // First, verify user is enrolled in this subject
    const { data: enrollment, error: enrollmentError } = await supabase
      .from('user_subjects')
      .select('id')
      .eq('user_id', userId)
      .eq('subject_id', subjectId)
      .eq('is_active', true)
      .single();

    if (enrollmentError || !enrollment) {
      return { data: null, error: 'Subject not found or access denied' };
    }

    // Get subject details
    const { data: subjectData, error: subjectError } = await supabase
      .from('subjects')
      .select('id, name, code, description, course_id, created_at')
      .eq('id', subjectId)
      .single();

    if (subjectError || !subjectData) {
      return { data: null, error: 'Subject not found' };
    }

    // Get all enrolled students for this subject
    const { data: studentsData, error: studentsError } = await supabase
      .from('user_subjects')
      .select(`
        user_id,
        enrolled_at,
        users:user_id (
          id,
          email,
          first_name,
          last_name
        )
      `)
      .eq('subject_id', subjectId)
      .eq('is_active', true) as { data: SubjectStudentsQueryResult[] | null; error: any };

    if (studentsError) {
      console.error('Error fetching students:', studentsError);
      return { data: null, error: 'Failed to load students' };
    }

    // Process student data
    const students: EnrolledStudent[] = (studentsData || [])
      .filter((student): student is SubjectStudentsQueryResult & { users: NonNullable<SubjectStudentsQueryResult['users']> } =>
        student.users !== null
      )
      .map(student => ({
        id: student.users.id.toString(),
        email: student.users.email,
        first_name: student.users.first_name || '',
        last_name: student.users.last_name || '',
        course: 'Computer Science', // TODO: Get actual course from database
        enrollment_date: student.enrolled_at,
        avatar_url: undefined
      }));

    const response: GetSubjectResponse = {
      subject: {
        id: subjectData.id,
        name: subjectData.name,
        code: subjectData.code || '',
        description: subjectData.description || undefined,
        course_id: subjectData.course_id,
        created_at: subjectData.created_at
      },
      students
    };

    return { data: response, error: null };

  } catch (error) {
    console.error('Database error getting subject details:', error);
    return { data: null, error: 'Failed to load subject details' };
  }
}

/**
 * Get all enrolled students for a subject
 */
export async function getSubjectStudents(subjectId: string, userId: string): Promise<{ data: EnrolledStudent[] | null; error: string | null }> {
  try {
    const supabase = await createClient();

    // Verify user is enrolled in this subject
    const { data: enrollment, error: enrollmentError } = await supabase
      .from('user_subjects')
      .select('id')
      .eq('user_id', userId)
      .eq('subject_id', subjectId)
      .eq('is_active', true)
      .single();

    if (enrollmentError || !enrollment) {
      return { data: null, error: 'Subject not found or access denied' };
    }

    // Get all enrolled students
    const { data: studentsData, error: studentsError } = await supabase
      .from('user_subjects')
      .select(`
        user_id,
        enrolled_at,
        users:user_id (
          id,
          email,
          first_name,
          last_name
        )
      `)
      .eq('subject_id', subjectId)
      .eq('is_active', true) as { data: SubjectStudentsQueryResult[] | null; error: any };

    if (studentsError) {
      console.error('Error fetching students:', studentsError);
      return { data: null, error: 'Failed to load students' };
    }

    const students: EnrolledStudent[] = (studentsData || [])
      .filter((student): student is SubjectStudentsQueryResult & { users: NonNullable<SubjectStudentsQueryResult['users']> } =>
        student.users !== null
      )
      .map(student => ({
        id: student.users.id.toString(),
        email: student.users.email,
        first_name: student.users.first_name || '',
        last_name: student.users.last_name || '',
        course: 'Computer Science', // TODO: Get actual course from database
        enrollment_date: student.enrolled_at,
        avatar_url: undefined
      }));

    return { data: students, error: null };

  } catch (error) {
    console.error('Database error getting subject students:', error);
    return { data: null, error: 'Failed to load students' };
  }
}

/**
 * Get subject statistics for a user
 */
export async function getSubjectStats(subjectId: string, userId: string): Promise<{ data: any | null; error: string | null }> {
  try {
    const supabase = await createClient();

    // Verify user is enrolled in this subject
    const { data: enrollment, error: enrollmentError } = await supabase
      .from('user_subjects')
      .select('enrolled_at')
      .eq('user_id', userId)
      .eq('subject_id', subjectId)
      .eq('is_active', true)
      .single();

    if (enrollmentError || !enrollment) {
      return { data: null, error: 'Subject not found or access denied' };
    }

    // Get attendance records for this user and subject
    const { data: attendanceData, error: attendanceError } = await supabase
      .from('attendance_records')
      .select('date, status')
      .eq('user_id', userId)
      .eq('subject_id', subjectId);

    if (attendanceError) {
      console.error('Error fetching attendance:', attendanceError);
      return { data: null, error: 'Failed to load attendance data' };
    }

    // Calculate stats
    const enrolledDate = new Date(enrollment.enrolled_at);
    const daysSinceEnrollment = Math.floor((Date.now() - enrolledDate.getTime()) / (1000 * 60 * 60 * 24));
    const estimatedTotalClasses = Math.max(1, Math.floor(daysSinceEnrollment / 7) * 3); // Assume 3 classes per week

    const attendedClasses = attendanceData?.length || 0;
    const attendancePercentage = estimatedTotalClasses > 0 ?
      Math.round((attendedClasses / estimatedTotalClasses) * 100 * 10) / 10 : 0;

    const stats = {
      total_classes: estimatedTotalClasses,
      attended_classes: attendedClasses,
      attendance_percentage: attendancePercentage,
      last_attendance: attendanceData && attendanceData.length > 0 ?
        attendanceData.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0].date :
        null
    };

    return { data: stats, error: null };

  } catch (error) {
    console.error('Database error getting subject stats:', error);
    return { data: null, error: 'Failed to load subject statistics' };
  }
}