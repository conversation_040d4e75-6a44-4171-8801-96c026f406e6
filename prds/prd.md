# College Student Platform - Complete Task Master Plan

## Step 0: Database & Infrastructure Setup
SQL migrations in supbase-migrations.md

### 0.1 Supabase Database Setup
- [x] Run the complete SQL migration in Supabase SQL Editor
- [x] Realtime enabled for 'messages' table (included in migration)
- [x] Realtime enabled for 'conversation_participants' table (included in migration)
- [x] Realtime enabled for 'conversations' table (included in migration)
- [x] Verify all tables are created with seed data

### 0.2 Storage Configuration
- [x] Go to Supabase Storage → Create new bucket
- [x] Name: `chat-files`
- [x] Set as **Public**
- [x] Upload size limit: 10MB
- [x] Allowed file types: `image/*`, `application/pdf`, `text/*`, `application/msword`, `application/vnd.openxmlformats-officedocument.*`

### 0.3 Environment Variables Setup
Create `.env.local` with:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
RESEND_API_KEY=your_resend_api_key
JWT_SECRET=your_jwt_secret_key
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

---

## Module 1: Authentication System 🔐

### User Stories:
- As a student, I want to sign up with my course selection and receive OTP via email
- As a student, I want to verify my email via OTP and auto-login
- As a student, I want to login and stay logged in with sessions
- As a student, I want to reset my password if forgotten

### 1.1 Project Setup & Dependencies
- [x] Create Next.js project: `npx create-next-app@latest college-platform --typescript --tailwind --eslint --app`
- [x] Install core dependencies:
  ```bash
  pnpm install @supabase/supabase-js bcryptjs jsonwebtoken resend
  pnpm install -D @types/bcryptjs @types/jsonwebtoken
  ```
- [x] Setup shadcn/ui: `pnpm dlx shadcn@latest init`
- [x] Install shadcn components: `pnpm dlx shadcn@latest add button input card form label toast skeleton avatar badge`

### 1.2 Core Library Setup
- [x] Create `lib/supabase.ts` - Supabase client configuration
- [x] Create `lib/auth.ts` - Password hashing, JWT utilities
- [x] Create `lib/email.ts` - Resend integration for OTP emails
- [x] Create `lib/validations.ts` - Zod schemas for form validation
- [x] Create `types/auth.ts` - TypeScript interfaces

### 1.3 Email System Implementation
- [x] Create `templates/otp-email.html` - Use your existing HTML template
- [x] Implement `sendOTPEmail()` function in `lib/email.ts`
- [x] Implement `generateOTP()` helper function
- [x] Create `sendPasswordResetEmail()` function
- [x] Test email delivery in development

### 1.4 Authentication Context & Hooks
- [x] Create `contexts/AuthContext.tsx` - Global auth state management
- [x] Create `hooks/useAuth.ts` - Auth hook with login/logout/user state
- [x] Create `components/auth/ProtectedRoute.tsx` - Route protection wrapper
- [x] Create `middleware.ts` - Next.js middleware for auth protection

### 1.5 Auth API Routes
- [x] Create `app/api/auth/signup/route.ts` - User registration + OTP sending
- [x] Create `app/api/auth/verify-otp/route.ts` - OTP verification + auto-login
- [x] Create `app/api/auth/login/route.ts` - User login + session creation
- [x] Create `app/api/auth/logout/route.ts` - Session destruction
- [x] Create `app/api/auth/resend-otp/route.ts` - Resend OTP functionality
- [x] Create `app/api/auth/forgot-password/route.ts` - Password reset initiation
- [x] Create `app/api/auth/reset-password/route.ts` - Password reset completion

### 1.6 Auth UI Components & Pages
- [x] Create `components/ui/AuthCard.tsx` - Reusable auth container
- [x] Create `components/auth/SignupForm.tsx` - Registration form with course selection
- [x] Create `components/auth/OTPForm.tsx` - OTP verification form
- [x] Create `components/auth/LoginForm.tsx` - Login form
- [x] Create `components/auth/ForgotPasswordForm.tsx` - Password reset request
- [x] Create `components/auth/ResetPasswordForm.tsx` - New password form
- [x] Create `app/auth/signup/page.tsx` - Signup page
- [x] Create `app/auth/verify-otp/page.tsx` - OTP verification page
- [x] Create `app/auth/login/page.tsx` - Login page
- [x] Create `app/auth/forgot-password/page.tsx` - Forgot password page
- [x] Create `app/auth/reset-password/[token]/page.tsx` - Reset password page

### 1.7 Auth Flow Integration & Testing
- [x] Implement complete signup → OTP → auto-login flow
- [x] Add loading states and error handling throughout
- [x] Add form validation with proper error messages
- [x] Test email delivery and OTP verification
- [x] Test session management and persistence
- [x] Add redirect logic after successful auth

---

## Module 2: Home Dashboard 🏠

### User Stories:
- As a logged-in student, I want to see all my enrolled subjects as cards
- As a student, I want to click on a subject card to view details
- As a student, I want to see my profile info and logout option

### 2.1 Dashboard Layout & Navigation
- [x] Create `components/layout/DashboardLayout.tsx` - Main layout with header/sidebar
- [x] Create `components/layout/Header.tsx` - Navigation with user info and logout
- [x] Create `components/layout/Sidebar.tsx` - Optional sidebar for navigation
- [x] Create `app/dashboard/layout.tsx` - Protected dashboard layout

### 2.2 Dashboard Data Layer
- [x] Create `lib/subjects.ts` - Subject-related database operations
- [x] Implement `getEnrolledSubjects(userId)` function
- [x] Implement `getSubjectStats(userId, subjectId)` for attendance counts
- [x] Create `types/subjects.ts` - Subject-related TypeScript interfaces

### 2.3 Subject Components
- [x] Create `components/dashboard/SubjectCard.tsx` - Individual subject display
- [x] Create `components/dashboard/SubjectGrid.tsx` - Responsive grid layout
- [x] Create `components/dashboard/DashboardStats.tsx` - Overall stats display
- [x] Create `components/ui/LoadingGrid.tsx` - Loading skeleton for subjects

### 2.4 Dashboard Pages
- [x] Create `app/dashboard/page.tsx` - Main dashboard with subject grid
- [x] Add welcome message with user's name and course
- [x] Implement search/filter for subjects (optional)
- [x] Add responsive design for mobile/tablet
- [x] Add empty state if no subjects found

### 2.5 Navigation & UX Polish
- [x] Add hover effects and animations to subject cards
- [x] Implement breadcrumb navigation
- [x] Add keyboard navigation support
- [x] Create loading states with shadcn Skeleton components
- [x] Add error boundaries for better error handling

---

## Module 3: Subject Detail & Student Management 👥

### User Stories:
- As a student, I want to see all students enrolled in a subject
- As a student, I want to start individual chats with classmates
- As a student, I want to join the group chat for this subject
- As a student, I want to quickly access attendance marking

### 3.1 Subject Detail Data Layer
- [ ] Create `lib/students.ts` - Student-related database operations
- [ ] Implement `getSubjectStudents(subjectId)` function
- [ ] Implement `getSubjectDetails(subjectId)` function
- [ ] Create `lib/conversations.ts` - Chat-related database operations
- [ ] Implement `getOrCreateDirectChat(userId1, userId2)` function
- [ ] Implement `getGroupChatBySubject(subjectId)` function

### 3.2 Subject Detail Components
- [ ] Create `components/subject/SubjectHeader.tsx` - Subject info display
- [ ] Create `components/subject/StudentCard.tsx` - Individual student card with chat button
- [ ] Create `components/subject/StudentGrid.tsx` - Grid layout for students
- [ ] Create `components/subject/SubjectActions.tsx` - Group chat, attendance buttons
- [ ] Create `components/subject/AttendanceQuickAction.tsx` - Quick attendance marking

### 3.3 Chat Integration
- [ ] Create `app/api/conversations/create-direct/route.ts` - Create direct chat API
- [ ] Create `app/api/conversations/get-group/route.ts` - Get group chat API
- [ ] Add chat navigation logic from student cards
- [ ] Add group chat button with proper routing
- [ ] Handle chat creation loading states

### 3.4 Subject Detail Pages
- [ ] Create `app/dashboard/subject/[id]/page.tsx` - Main subject detail page
- [ ] Add subject information header
- [ ] Display enrolled students grid
- [ ] Add action buttons for group chat and attendance
- [ ] Implement responsive design for mobile
- [ ] Add loading and error states

### 3.5 Navigation & Polish
- [ ] Add back navigation to dashboard
- [ ] Implement search/filter for students
- [ ] Add online status indicators (optional advanced feature)
- [ ] Create proper page titles and metadata
- [ ] Add breadcrumb navigation

---

## Module 4: Real-time Chat System 💬

### User Stories:
- As a student, I want to chat with other students in real-time
- As a student, I want to send text messages and files
- As a student, I want to see message history and timestamps
- As a student, I want to distinguish between direct and group chats

### 4.1 Realtime Infrastructure
- [ ] Verify Supabase realtime setup is complete
- [ ] Create `hooks/useRealtimeMessages.ts` - Real-time message subscription
- [ ] Create `hooks/useRealtimeTyping.ts` - Typing indicators (optional)
- [ ] Create `lib/realtime.ts` - Realtime connection management

### 4.2 Chat Data Layer
- [ ] Create `lib/messages.ts` - Message-related database operations
- [ ] Implement `getMessages(conversationId, limit, offset)` function
- [ ] Implement `sendTextMessage(conversationId, senderId, content)` function
- [ ] Implement `sendFileMessage(conversationId, senderId, file)` function
- [ ] Create `lib/storage.ts` - File upload to Supabase storage
- [ ] Implement file upload with progress tracking

### 4.3 Chat UI Components
- [ ] Create `components/chat/ChatLayout.tsx` - Chat page layout
- [ ] Create `components/chat/ChatHeader.tsx` - Chat title, participants, back button
- [ ] Create `components/chat/MessageList.tsx` - Scrollable message container
- [ ] Create `components/chat/MessageBubble.tsx` - Individual message display
- [ ] Create `components/chat/MessageInput.tsx` - Message composition with file upload
- [ ] Create `components/chat/FilePreview.tsx` - File attachment display
- [ ] Create `components/chat/TypingIndicator.tsx` - Typing status display

### 4.4 File Upload System
- [ ] Create `components/chat/FileUpload.tsx` - Drag & drop file upload
- [ ] Implement file validation (size, type restrictions)
- [ ] Add upload progress indicators
- [ ] Create file preview for images
- [ ] Handle different file types (documents, images)
- [ ] Add file download functionality

### 4.5 Chat API Routes
- [ ] Create `app/api/messages/send/route.ts` - Send message API
- [ ] Create `app/api/messages/[conversationId]/route.ts` - Get messages API
- [ ] Create `app/api/upload/route.ts` - File upload API
- [ ] Add message pagination support
- [ ] Add read receipt functionality (optional)

### 4.6 Chat Pages & Navigation
- [ ] Create `app/chat/[conversationId]/page.tsx` - Main chat interface
- [ ] Implement auto-scroll to bottom for new messages
- [ ] Add message timestamps and sender info
- [ ] Handle chat loading and error states
- [ ] Add proper navigation back to subject/dashboard
- [ ] Implement keyboard shortcuts (Enter to send, etc.)

### 4.7 Real-time Integration & Polish
- [ ] Test real-time message delivery
- [ ] Add connection status indicators
- [ ] Handle network disconnection gracefully
- [ ] Add message delivery status (sent, delivered)
- [ ] Optimize performance for large chat histories
- [ ] Add proper cleanup on component unmount

---

## Module 5: Attendance System 📅

### User Stories:
- As a student, I want to mark my attendance for a subject today
- As a student, I want to view my attendance history in calendar format
- As a student, I want to see my attendance percentage and stats

### 5.1 Attendance Data Layer
- [ ] Create `lib/attendance.ts` - Attendance-related database operations
- [ ] Implement `markAttendance(userId, subjectId, date)` function
- [ ] Implement `getAttendanceHistory(userId, subjectId)` function
- [ ] Implement `getAttendanceStats(userId, subjectId)` function
- [ ] Implement `checkTodayAttendance(userId, subjectId)` function
- [ ] Add attendance validation logic

### 5.2 Attendance Components
- [ ] Create `components/attendance/AttendanceCalendar.tsx` - Calendar view with shadcn Calendar
- [ ] Create `components/attendance/AttendanceStats.tsx` - Statistics display
- [ ] Create `components/attendance/MarkAttendanceCard.tsx` - Today's attendance marking
- [ ] Create `components/attendance/AttendanceHistory.tsx` - History list view
- [ ] Create `components/ui/ProgressRing.tsx` - Circular progress for percentages

### 5.3 Attendance API Routes
- [ ] Create `app/api/attendance/mark/route.ts` - Mark attendance API
- [ ] Create `app/api/attendance/history/[subjectId]/route.ts` - Get history API
- [ ] Create `app/api/attendance/stats/[subjectId]/route.ts` - Get stats API
- [ ] Add validation to prevent duplicate marking
- [ ] Add date validation (can't mark future dates)

### 5.4 Attendance Pages
- [ ] Create `app/dashboard/subject/[id]/attendance/page.tsx` - Main attendance page
- [ ] Add calendar view with marked dates
- [ ] Display attendance statistics and percentages
- [ ] Add mark attendance functionality
- [ ] Show attendance streaks and achievements (optional)
- [ ] Implement responsive design for mobile

### 5.5 Attendance Integration
- [ ] Add attendance quick actions to subject detail page
- [ ] Add attendance stats to dashboard subject cards
- [ ] Create attendance notification system (optional)
- [ ] Add attendance goals and reminders (optional)
- [ ] Integrate with overall dashboard stats

---

## Module 6: Polish, Testing & Deployment 🚀

### 6.1 UI/UX Polish & Accessibility
- [ ] Implement consistent loading states throughout app
- [ ] Add error boundaries with proper error pages
- [ ] Add toast notifications using shadcn Toast
- [ ] Test responsive design on mobile, tablet, desktop
- [ ] Add keyboard navigation and accessibility features
- [ ] Implement proper focus management
- [ ] Add proper ARIA labels and semantic HTML

### 6.2 Performance Optimization
- [ ] Add proper TypeScript types throughout
- [ ] Optimize database queries with proper indexing
- [ ] Implement image optimization for avatars/files
- [ ] Add proper loading and error states
- [ ] Implement code splitting with React.lazy
- [ ] Optimize bundle size and Core Web Vitals
- [ ] Add proper caching strategies

### 6.3 Security & Validation
- [ ] Add comprehensive input validation and sanitization
- [ ] Implement rate limiting for API routes
- [ ] Add CSRF protection
- [ ] Audit and improve RLS policies in Supabase
- [ ] Add proper error logging and monitoring
- [ ] Test security vulnerabilities

### 6.4 Testing & Quality Assurance
- [ ] Write unit tests for utility functions
- [ ] Write integration tests for API routes
- [ ] Test all user flows end-to-end
- [ ] Test real-time functionality thoroughly
- [ ] Test file upload edge cases
- [ ] Cross-browser testing
- [ ] Performance testing with large datasets

### 6.5 Documentation & Deployment
- [ ] Create comprehensive README.md
- [ ] Document API endpoints and database schema
- [ ] Set up environment variables for production
- [ ] Deploy to Vercel/Netlify
- [ ] Configure custom domain and SSL
- [ ] Set up monitoring and analytics
- [ ] Create backup and recovery procedures

### 6.6 Advanced Features (Optional)
- [ ] Add push notifications for new messages
- [ ] Implement offline support with service workers
- [ ] Add voice/video calling functionality
- [ ] Create admin panel for teachers/administrators
- [ ] Add file sharing and collaborative documents
- [ ] Implement user profiles with avatars
- [ ] Add dark mode support
- [ ] Create mobile app with React Native (future)

---

## Development Guidelines & Best Practices

### 🎨 shadcn/ui Components to Use:
**Core Components:**
- `Button`, `Input`, `Card`, `Form`, `Label` - Basic form elements
- `Dialog`, `Sheet`, `Popover` - Modals and overlays
- `Avatar`, `Badge`, `Progress` - User interface elements
- `Calendar`, `ScrollArea`, `Separator` - Layout components
- `Skeleton`, `Toast`, `Alert` - Feedback components

**Chat-Specific:**
- `ScrollArea` for message lists
- `Sheet` for mobile chat drawer
- `Popover` for emoji picker (future)
- `Progress` for file upload progress

### 📱 Responsive Design Strategy:
- **Mobile-first approach** - Design for mobile, enhance for desktop
- **Breakpoints**: `sm: 640px`, `md: 768px`, `lg: 1024px`, `xl: 1280px`
- **Chat UI**: Full-screen on mobile, sidebar on desktop
- **Subject grid**: 1 column mobile, 2-3 columns tablet, 4+ desktop
- **Navigation**: Hamburger menu on mobile, full nav on desktop

### ⚡ Performance Best Practices:
- **Images**: Use Next.js Image component with proper optimization
- **Code Splitting**: Lazy load chat components and heavy features
- **Database**: Implement pagination for messages and large lists
- **Real-time**: Unsubscribe from channels on component unmount
- **Caching**: Cache subject lists and user data appropriately

### 🔐 Security Considerations:
- **Input Validation**: Validate all inputs on both client and server
- **File Uploads**: Restrict file types and sizes, scan for malware
- **Rate Limiting**: Prevent spam in chat and attendance marking
- **Authentication**: Proper session management and token expiration
- **Database**: Use RLS policies and prepared statements

### 🧪 Testing Strategy:
**Unit Tests:**
- Auth utilities (password hashing, JWT)
- Validation functions
- Database query functions

**Integration Tests:**
- API route handlers
- Database operations
- Email sending functionality

**E2E Tests:**
- Complete user registration flow
- Chat functionality
- Attendance marking
- Cross-browser compatibility

### 📁 Project Structure:
```
college-platform/
├── app/                          # Next.js app router
│   ├── api/                      # API routes
│   │   ├── auth/                 # Authentication endpoints
│   │   ├── messages/             # Chat endpoints
│   │   ├── attendance/           # Attendance endpoints
│   │   └── upload/               # File upload endpoints
│   ├── auth/                     # Auth pages
│   ├── dashboard/                # Protected dashboard pages
│   └── chat/                     # Chat pages
├── components/                   # React components
│   ├── auth/                     # Authentication components
│   ├── chat/                     # Chat components
│   ├── dashboard/                # Dashboard components
│   ├── layout/                   # Layout components
│   ├── subject/                  # Subject-related components
│   ├── attendance/               # Attendance components
│   └── ui/                       # shadcn/ui components
├── contexts/                     # React contexts
├── hooks/                        # Custom React hooks
├── lib/                          # Utility functions and configurations
├── types/                        # TypeScript type definitions
├── templates/                    # Email templates
└── middleware.ts                 # Next.js middleware
```

---

## Quick Start Development Order

### Phase 1: Foundation (Week 1)
1. **Database Setup** (Day 1)
   - Run SQL migration
   - Configure Supabase settings
   - Create storage bucket

2. **Project Setup** (Day 2)
   - Initialize Next.js project
   - Install dependencies
   - Setup shadcn/ui
   - Configure environment variables

3. **Authentication Core** (Days 3-5)
   - Create auth utilities
   - Build signup/login forms
   - Implement OTP email system
   - Test complete auth flow

4. **Dashboard Foundation** (Days 6-7)
   - Create dashboard layout
   - Build subject grid
   - Add navigation
   - Test with sample data

### Phase 2: Core Features (Week 2)
1. **Subject Details** (Days 1-2)
   - Subject detail page
   - Student listing
   - Chat navigation setup

2. **Chat System** (Days 3-6)
   - Build chat UI components
   - Implement real-time messaging
   - Add file upload functionality
   - Test thoroughly

3. **Attendance System** (Day 7)
   - Attendance marking
   - Calendar view
   - Basic statistics

### Phase 3: Polish & Deploy (Week 3)
1. **UI/UX Polish** (Days 1-3)
   - Responsive design
   - Loading states
   - Error handling
   - Accessibility

2. **Testing & Optimization** (Days 4-5)
   - End-to-end testing
   - Performance optimization
   - Security audit

3. **Deployment** (Days 6-7)
   - Production deployment
   - Domain setup
   - Monitoring setup

---

## Success Metrics & Validation

### 📊 Technical Metrics:
- **Performance**: Page load < 3s, First Contentful Paint < 1.5s
- **Real-time**: Message delivery < 500ms
- **Uptime**: 99.9% availability
- **Security**: No critical vulnerabilities

### 👥 User Experience Metrics:
- **Registration**: < 2 minutes from signup to dashboard
- **Chat**: Intuitive WhatsApp-like experience
- **Attendance**: One-click marking
- **Mobile**: Fully functional on mobile devices

### 🚀 Completion Criteria:
- [ ] All 6 modules completed and tested
- [ ] Production deployment successful
- [ ] Real-time chat working reliably
- [ ] Email system functioning
- [ ] Responsive design on all devices
- [ ] Security best practices implemented

---

## Troubleshooting Guide

### Common Issues & Solutions:

**Supabase Connection Issues:**
- Verify environment variables
- Check RLS policies
- Confirm realtime is enabled

**Email Delivery Issues:**
- Test Resend API key
- Check spam folders
- Verify domain configuration

**Real-time Chat Issues:**
- Confirm realtime subscription setup
- Check network connectivity
- Verify table replication settings

**File Upload Issues:**
- Check storage bucket permissions
- Verify file size limits
- Test with different file types

This comprehensive plan provides a clear roadmap from setup to deployment, with each module building upon the previous one. The modular approach allows you to complete and test each section independently before moving to the next!