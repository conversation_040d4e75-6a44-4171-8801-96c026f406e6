import { createClient } from '@/lib/supabase/server';
import { ConversationData, CreateChatResponse } from '../types';

/**
 * Server-side API functions for conversation operations
 */

/**
 * Create or retrieve direct conversation between current user and another user
 */
export async function getOrCreateDirectChat(userId: string, participantId: string): Promise<{ data: CreateChatResponse | null; error: string | null }> {
  try {
    const supabase = await createClient();

    // Validate that both users exist
    const { data: participantData, error: participantError } = await supabase
      .from('users')
      .select('id')
      .eq('id', participantId)
      .single();

    if (participantError || !participantData) {
      return { data: null, error: 'Participant not found' };
    }

    // Check if a direct conversation already exists between these users
    const { data: existingConversation, error: existingError } = await supabase
      .from('conversations')
      .select('id')
      .eq('type', 'direct')
      .contains('participants', [userId, participantId])
      .single();

    if (!existingError && existingConversation) {
      // Return existing conversation
      return {
        data: {
          conversation_id: existingConversation.id,
          redirect_url: `/chat/${existingConversation.id}`
        },
        error: null
      };
    }

    // Create new direct conversation
    const { data: newConversation, error: createError } = await supabase
      .from('conversations')
      .insert({
        type: 'direct',
        participants: [userId, participantId],
        created_by: userId,
        created_at: new Date().toISOString()
      })
      .select('id')
      .single();

    if (createError || !newConversation) {
      console.error('Error creating conversation:', createError);
      return { data: null, error: 'Failed to create conversation' };
    }

    return {
      data: {
        conversation_id: newConversation.id,
        redirect_url: `/chat/${newConversation.id}`
      },
      error: null
    };

  } catch (error) {
    console.error('Database error creating direct chat:', error);
    return { data: null, error: 'Failed to create chat' };
  }
}

/**
 * Get or create group chat for a specific subject
 */
export async function getGroupChatBySubject(subjectId: string, userId: string): Promise<{ data: ConversationData | null; error: string | null }> {
  try {
    const supabase = await createClient();

    // Verify user is enrolled in this subject
    const { data: enrollment, error: enrollmentError } = await supabase
      .from('user_subjects')
      .select('id')
      .eq('user_id', userId)
      .eq('subject_id', subjectId)
      .eq('is_active', true)
      .single();

    if (enrollmentError || !enrollment) {
      return { data: null, error: 'Subject not found or access denied' };
    }

    // Check if group conversation already exists for this subject
    const { data: existingConversation, error: existingError } = await supabase
      .from('conversations')
      .select('id, type, participants, subject_id, created_at')
      .eq('type', 'group')
      .eq('subject_id', subjectId)
      .single();

    if (!existingError && existingConversation) {
      return { data: existingConversation, error: null };
    }

    // Get all enrolled students for this subject to create group chat
    const { data: studentsData, error: studentsError } = await supabase
      .from('user_subjects')
      .select('user_id')
      .eq('subject_id', subjectId)
      .eq('is_active', true);

    if (studentsError || !studentsData) {
      return { data: null, error: 'Failed to load subject participants' };
    }

    const participants = studentsData.map(student => student.user_id);

    // Create new group conversation
    const { data: newConversation, error: createError } = await supabase
      .from('conversations')
      .insert({
        type: 'group',
        participants,
        subject_id: subjectId,
        created_by: userId,
        created_at: new Date().toISOString()
      })
      .select('id, type, participants, subject_id, created_at')
      .single();

    if (createError || !newConversation) {
      console.error('Error creating group conversation:', createError);
      return { data: null, error: 'Failed to create group conversation' };
    }

    return { data: newConversation, error: null };

  } catch (error) {
    console.error('Database error getting group chat:', error);
    return { data: null, error: 'Failed to load group chat' };
  }
}

/**
 * Create a new conversation
 */
export async function createConversation(
  type: 'direct' | 'group',
  participants: string[],
  createdBy: string,
  subjectId?: string
): Promise<{ data: ConversationData | null; error: string | null }> {
  try {
    const supabase = await createClient();

    // Validate participants exist
    const { data: participantData, error: participantError } = await supabase
      .from('users')
      .select('id')
      .in('id', participants);

    if (participantError || !participantData || participantData.length !== participants.length) {
      return { data: null, error: 'One or more participants not found' };
    }

    // Create conversation
    const { data: conversation, error: createError } = await supabase
      .from('conversations')
      .insert({
        type,
        participants,
        subject_id: subjectId,
        created_by: createdBy,
        created_at: new Date().toISOString()
      })
      .select('id, type, participants, subject_id, created_at')
      .single();

    if (createError || !conversation) {
      console.error('Error creating conversation:', createError);
      return { data: null, error: 'Failed to create conversation' };
    }

    return { data: conversation, error: null };

  } catch (error) {
    console.error('Database error creating conversation:', error);
    return { data: null, error: 'Failed to create conversation' };
  }
}