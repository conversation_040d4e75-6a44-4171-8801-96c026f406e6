import { DashboardData } from '../types/dashboard.types'
import apiClient from '@/lib/axios';

/**
 * Client-side API functions for dashboard
 */
export class DashboardClient {
  /**
   * Fetch dashboard data for the current user
   */
  static async getDashboardData(): Promise<{ data: DashboardData | null; error: string | null }> {
    try {
      const response = await apiClient.get('/dashboard/subjects');
      return { data: response.data, error: null };
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      return { data: null, error: 'Network error occurred' }
    }
  }

  /**
   * Refresh dashboard data
   */
  static async refreshDashboard(): Promise<{ data: DashboardData | null; error: string | null }> {
    return this.getDashboardData()
  }
}