'use client';

import { useAuth, useAuthActions, useAuthStore } from '@/stores/auth-store';
import { useEffect } from 'react';

/**
 * Custom hook that provides auth state and actions
 * This is a convenience hook that combines all auth functionality
 */
export function useAuthHook() {
   const authState = useAuth();
   const authActions = useAuthActions();
   // const initializeAuth = useAuthStore((state) => state.initializeAuth);
   const initializeAuth = useAuthStore.getState().initializeAuth;

   // Initialize auth on mount
   useEffect(() => {
      initializeAuth();
   }, [initializeAuth]);

   return {
      // Auth state
      ...authState,

      // Auth actions
      ...authActions,

      // Computed properties
      isInitialized: !authState.isLoading,

      // Helper methods
      requireAuth: () => {
         if (!authState.isAuthenticated) {
            throw new Error('User must be authenticated');
         }
         return authState.user;
      },

      requireVerifiedUser: () => {
         if (!authState.isAuthenticated || !authState.user?.emailVerified) {
            throw new Error('User must be authenticated and verified');
         }
         return authState.user;
      },
   };
}

/**
 * Hook for components that require authenticated user
 */
export function useRequireAuth() {
   const auth = useAuthHook();

   useEffect(() => {
      if (auth.isInitialized && !auth.isAuthenticated) {
         // Could redirect to login or throw error
         console.warn('useRequireAuth: User is not authenticated');
      }
   }, [auth.isInitialized, auth.isAuthenticated]);

   return auth;
}

/**
 * Hook for components that require verified user
 */
export function useRequireVerifiedAuth() {
   const auth = useAuthHook();

   useEffect(() => {
      if (auth.isInitialized) {
         if (!auth.isAuthenticated) {
            console.warn('useRequireVerifiedAuth: User is not authenticated');
         } else if (!auth.user?.emailVerified) {
            console.warn('useRequireVerifiedAuth: User email is not verified');
         }
      }
   }, [auth.isInitialized, auth.isAuthenticated, auth.user?.emailVerified]);

   return auth;
}

// Re-export for convenience
export { useAuth, useAuthActions } from '@/stores/auth-store';
