'use client';

import { create } from 'zustand';
import { DashboardState } from '../types/dashboard.types';
import { DashboardClient } from '../api/dashboard-client';

interface DashboardActions {
   fetchDashboardData: () => Promise<void>;
   refreshSubjects: () => Promise<void>;
   setLoading: (loading: boolean) => void;
   setError: (error: string | null) => void;
   clearError: () => void;
}

type DashboardStore = DashboardState & DashboardActions;

export const useDashboardStore = create<DashboardStore>((set, get) => ({
   // Initial state
   data: null,
   loading: false,
   error: null,

   // Actions
   fetchDashboardData: async () => {
      set({ loading: true, error: null });

      try {
         const { data, error } = await DashboardClient.getDashboardData();

         if (error) {
            set({ loading: false, error });
         } else {
            set({ loading: false, data, error: null });
         }
      } catch (error) {
         console.error('Error fetching dashboard data:', error);
         set({ loading: false, error: 'Failed to load dashboard data' });
      }
   },

   refreshSubjects: async () => {
      const { fetchDashboardData } = get();
      await fetchDashboardData();
   },

   setLoading: (loading: boolean) => {
      set({ loading });
   },

   setError: (error: string | null) => {
      set({ error });
   },

   clearError: () => {
      set({ error: null });
   },
}));
