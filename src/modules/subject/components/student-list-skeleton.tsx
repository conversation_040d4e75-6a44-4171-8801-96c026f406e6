'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export function StudentListSkeleton() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {Array.from({ length: 8 }).map((_, index) => (
        <Card key={index} className="animate-pulse">
          <CardContent className="p-4">
            <div className="flex flex-col items-center text-center space-y-3">
              {/* Avatar Skeleton */}
              <Skeleton className="w-16 h-16 rounded-full" />

              {/* Name Skeleton */}
              <div className="space-y-1 w-full">
                <Skeleton className="h-4 w-3/4 mx-auto" />
                <Skeleton className="h-3 w-full" />
                <Skeleton className="h-3 w-2/3 mx-auto" />
              </div>

              {/* Date Skeleton */}
              <Skeleton className="h-2 w-1/2" />

              {/* Button Skeleton */}
              <Skeleton className="h-8 w-full" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}